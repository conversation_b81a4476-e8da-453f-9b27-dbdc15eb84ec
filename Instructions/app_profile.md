# App Profile

CropCompass is a web-based agricultural management system built with Django (backend) and React (frontend) that helps farmers optimize their crop management and yields.


## Core Features:

- Field management with GIS support
- Crop recommendations based on location and conditions
- Plant spacing and row spacing recommendations
- Cost estimates based on location
- Historical data analysis
- Weather data integration and forecasting
- Irrigation recommendations
- Soil health monitoring
- Historical data analysis
- Yield predictions
- Subscription-based access tiers


## Technical Stack:

- Backend: Django 5.1 with GeoDjango for spatial operations
- Frontend: React 18
- Database: PostgreSQL with PostGIS
- Maps: Leaflet
- Data Visualization: Plotly, Nivo
- Authentication: JWT

The system uses a RESTful API architecture where the frontend communicates with the backend through various endpoints (`/api/weather/`, `/api/recommendations/`, etc.). It's designed to be mobile-responsive and includes offline capabilities. Features a dark mode and high contrast mode. The application leverages geospatial data processing to provide location-specific recommendations for farming operations.

The project follows a modular structure with separate Django apps for different functionalities (weather, recommendations, historical_data, etc.) and a React frontend organized into components, pages, and services.

-----

## Important Files
@/Users/<USER>/Coding/Project_CC/Instructions/my_rules.md
@/Users/<USER>/Coding/Project_CC/Instructions/Product Requirements Document.md
@/Users/<USER>/Coding/Project_CC/Instructions/User Interface Design Document.md
@/Users/<USER>/Coding/Project_CC/Instructions/Software Requirements Specification.md