# UKAMA Theme Colours & Asset Catalog Instructions

This document lists the colours derived from the UKAMA Coffee website CSS theme and provides instructions for adding them to the Xcode Asset Catalog (`Assets.xcassets`) for use in the CropCompass GPS app.

## Colour Definitions for Asset Catalog

Create a new "Color Set" in `Assets.xcassets` for each item below. Use the suggested name and set the Hex values for "Any Appearance" (Light) and "Dark Appearance" (Dark).

```
// --- Primary Colours --- 

Asset Name: ThemePrimaryColor
  Light: #3a7d44
  Dark:  #4e9f5b

Asset Name: PrimaryDarkColor
  Light: #2c6134
  Dark:  #3a7d44

Asset Name: PrimaryLightColor
  Light: #4e8d50
  Dark:  #5aac67

Asset Name: ThemeSecondaryColor
  Light: #6a994e
  Dark:  #7baa5f

Asset Name: AccentColor
  Light: #b87333
  Dark:  #c98a56

// --- Text Colours --- 

Asset Name: TextColor
  Light: #2a2a2a
  Dark:  #f0f0f0

Asset Name: TextLightColor
  Light: #6b7280
  Dark:  #b0b0b0

// --- Background Colours --- 

Asset Name: BackgroundColor
  Light: #ffffff
  Dark:  #121212

Asset Name: BackgroundLightColor
  Light: #f9fafb
  Dark:  #1a1a1a

Asset Name: BackgroundAltColor
  Light: #f0f2f5
  Dark:  #2a2a2a

Asset Name: CardBackgroundColor
  Light: #ffffff
  Dark:  #1e1e1e

// --- Utility Colours --- 

Asset Name: BorderColor
  Light: #e5e7eb
  Dark:  #3a3a3a

Asset Name: SuccessColor
  Light: #10b981
  Dark:  #10b981

Asset Name: ErrorColor
  Light: #ef4444
  Dark:  #ef4444

Asset Name: WarningColor
  Light: #f59e0b
  Dark:  #f59e0b
```

## Instructions for Adding to Assets.xcassets

1.  **Open Asset Catalog:** In Xcode, navigate to your `Assets.xcassets` file.
2.  **Add New Color Set:** Click the '+' button -> "Color Set".
3.  **Rename:** Select the new set and rename it using the "Asset Name" from the list above (e.g., `ThemePrimaryColor`).
4.  **Configure Appearances:** In the Attributes Inspector, under "Appearances", select "Any, Dark".
5.  **Set Colors:** 
    *   Select the "Any Appearance" swatch, set Input Method to "Hexadecimal", paste the corresponding "Light" hex value.
    *   Select the "Dark Appearance" swatch, set Input Method to "Hexadecimal", paste the corresponding "Dark" hex value.
6.  **Repeat:** Repeat steps 2-5 for each colour asset name in the list.

## Using the Colours in SwiftUI

Once defined in the Asset Catalog, you can reference these colours directly in your SwiftUI code using their names:

```swift
Text("Primary Text")
    .foregroundColor(Color("TextColor"))

VStack { /* ... */ }
    .background(Color("BackgroundColor"))

Button("Submit") { /* ... */ }
    .buttonStyle(.borderedProminent)
    .tint(Color("ThemePrimaryColor")) // Use .tint for prominent button background
``` 