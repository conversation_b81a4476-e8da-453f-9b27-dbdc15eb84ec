# Original Colour Theme (Effective Light Mode - Pre-Website Theme)

This document records the approximate colours used throughout the CropCompass GPS app **as rendered in Light Mode** before applying the theme based on the UKAMA Coffee website CSS. It serves as a reference point.

Note: Many colours are based on SwiftUI's semantic or standard system colours, which adapt automatically. The hex codes provided are approximations for the Light Mode appearance.

## Backgrounds

*   **Main Background:** `Color(.systemBackground)` -> White (`#FFFFFF` approx)
*   **Secondary Background (e.g., Home sections):** `Color(.secondarySystemBackground)` -> Off-white/Light Gray (`#F2F2F7` approx)
*   **Tertiary Background (e.g., Field Selector):** `Color(.tertiarySystemBackground)` -> Often grouped with Secondary (`#FFFFFF` with visual grouping)
*   **Card Backgrounds (e.g., Edit sections):** `Color(.systemBackground)` -> White (`#FFFFFF` approx)
*   **Button Backgrounds (Opacity):**
    *   `Color.blue.opacity(0.1)` (e.g., Start Recording, Save to Files)
    *   `Color.red.opacity(0.1)` (e.g., Stop Recording)
    *   `Color.green.opacity(0.1)` (e.g., Mark Spot, Share)
*   **Input Field Background:** `Color(.systemGray6)` -> Very Light Gray (`#F2F2F7` approx)

## Text

*   **Primary Text:** Default (`Color.primary`) -> Black (`#000000` approx)
*   **Secondary Text:** `Color.secondary` -> Medium Gray (`#8E8E93` approx)
*   **Accent Text (Links, some buttons):** Default `Color.accentColor` or explicit `Color.blue` -> System Blue (`#007AFF` approx)

## Accents & Indicators

*   **Accent Color (Default):** `Color.accentColor` -> System Blue (`#007AFF` approx)
*   **Map Marker Tints:**
    *   Marked: `Color.red` -> System Red (`#FF3B30` approx)
    *   Boundary: `Color.orange` -> System Orange (`#FF9500` approx)
    *   Continuous: `Color.blue` -> System Blue (`#007AFF` approx)
*   **Button Icons/Text:** Often `.blue`, `.red`, `.green` corresponding to background tints or action type.
*   **GPS Accuracy Indicator:** Varies (`.green`, `.orange`, `.red`) based on accuracy.
*   **Checkmark (Export View):** `Color.blue` -> System Blue (`#007AFF` approx)

## Map Elements

*   **Polyline Stroke:** `.stroke(.blue, ...)` -> System Blue (`#007AFF` approx)
*   **Polygon Fill:** `.foregroundStyle(.blue.opacity(0.15))` -> System Blue with low opacity

## Borders & Dividers

*   Standard system appearance (typically light gray). 