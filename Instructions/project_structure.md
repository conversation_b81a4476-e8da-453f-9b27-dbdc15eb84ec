# Project Structure

CropCompass/
├── backend/
│   ├── manage.py                    # Django's command-line utility for administrative tasks
│   ├── requirements/                # Separated requirements for different environments
│   │   ├── base.txt                # Core dependencies: Django 5.1, DRF 3.15, GDA<PERSON>, PostGIS
│   │   ├── development.txt         # Development tools: debug toolbar, pytest, coverage
│   │   └── production.txt          # Production-specific: gunicorn, sentry-sdk
│   │
│   ├── .env.example                # Template for environment variables (DB, API keys, etc.)
│   │
│   ├── cropcompass/                # Django project root (main configuration)
│   │   ├── __init__.py
│   │   ├── settings/               # Layered settings for different environments
│   │   │   ├── __init__.py
│   │   │   ├── base.py            # Shared settings across all environments
│   │   │   ├── development.py     # Local development settings (DEBUG=True)
│   │   │   └── production.py      # Production settings (optimised for shared hosting)
│   │   ├── urls.py                # Main URL routing configuration
│   │   ├── asgi.py                # ASGI configuration for async support
│   │   └── wsgi.py                # WSGI configuration for production deployment
│   │
│   ├── apps/                       # Django applications directory
│   │   ├── users/                  # User & subscription management
│   │   │   ├── models.py          # Custom user model with subscription integration
│   │   │   ├── serializers.py     # JWT-compatible user serializers
│   │   │   ├── views.py           # Authentication and user management views
│   │   │   ├── permissions.py     # Subscription-based access control
│   │   │   └── tests/             # User-related test suites
│   │   │
│   │   ├── fields/                # Field management with GeoDjango
│   │   │   ├── models.py          # GeoDjango-powered field models
│   │   │   ├── serializers.py     # Field data serializers with GeoJSON support
│   │   │   ├── services/          # Field-related business logic
│   │   │   │   ├── spacing.py     # Plant and row spacing calculations
│   │   │   │   ├── orientation.py # Field orientation optimisation
│   │   │   │   └── gps_processor.py # GPS data validation and processing
│   │   │   └── tests/
│   │   │
│   │   ├── recommendations/        # Farming recommendations engine
│   │   │   ├── models.py          # Recommendation models
│   │   │   ├── services/          # Core calculation engines
│   │   │   │   ├── water_calculator.py    # Water requirement algorithms
│   │   │   │   ├── irrigation.py          # Irrigation scheduling
│   │   │   │   └── cost_estimator.py      # Location-based cost calculation
│   │   │   └── tests/
│   │   │
│   │   ├── weather/               # Weather integration (OpenWeather API)
│   │   │   ├── models.py          # Weather data models with caching
│   │   │   ├── services/          # Weather data processing
│   │   │   │   └── weather_service.py # API integration and data formatting
│   │   │   └── tests/
│   │   │
│   │   ├── subscriptions/         # Subscription tier management
│   │   │   ├── models.py          # Subscription plans and features
│   │   │   ├── services/          # Subscription logic handlers
│   │   │   └── tests/
│   │   │
│   │   └── historical_data/       # Historical data analysis
│   │       ├── models.py          # Time-series data models
│   │       ├── services/          # Analysis engines
│   │       │   ├── yield_analyzer.py    # Yield prediction
│   │       │   └── trend_calculator.py   # Trend analysis
│   │       └── tests/
│   │
│   ├── core/                      # Shared functionality across apps
│   │   ├── middleware/            # Custom middleware
│   │   │   └── offline_sync.py    # Offline data synchronisation
│   │   ├── permissions/           # Custom permissions
│   │   │   └── subscription_based.py # Tier-based access control
│   │   └── utils/                 # Shared utilities
│   │       ├── geo_utils.py       # GeoDjango helpers
│   │       └── weather_utils.py   # Weather data processors
│   │
│   └── api/                       # API version management
│       ├── v1/                    # API version 1
│       │   ├── urls.py            # API routing
│       │   └── views/             # API endpoints
│       └── tests/                 # API integration tests

├── frontend/                      # React application
│   ├── package.json              # NPM dependencies and scripts
│   ├── tsconfig.json             # TypeScript configuration
│   ├── .env.example              # Frontend environment variables
│   ├── public/
│   │   ├── index.html            # HTML entry point
│   │   ├── manifest.json         # PWA configuration
│   │   └── serviceWorker.js      # Offline functionality
│   │
│   └── src/
│       ├── components/           # React components
│       │   ├── ui/              # Reusable UI components
│       │   │   ├── Button/      # Custom button components
│       │   │   ├── Card/        # Data display cards
│       │   │   └── Form/        # Form components
│       │   ├── layout/          # Layout components
│       │   │   ├── Navbar/      # Navigation bar
│       │   │   └── Sidebar/     # Collapsible sidebar
│       │   └── features/        # Feature-specific components
│       │       ├── fields/      # Field management
│       │       │   ├── FieldMap/    # Leaflet map integration
│       │       │   └── GpsInput/    # GPS coordinate input
│       │       ├── weather/     # Weather display
│       │       └── recommendations/ # Recommendation displays
│       │
│       ├── pages/               # Page components
│       │   ├── Dashboard/       # Main dashboard with metrics
│       │   ├── Fields/         # Field management interface
│       │   ├── Reports/        # Data analysis and reports
│       │   └── Settings/       # User preferences
│       │
│       ├── services/           # External service integrations
│       │   ├── api/           # Backend API communication
│       │   ├── weather/       # Weather API integration
│       │   ├── maps/          # Mapping services
│       │   └── offline/       # Offline data management
│       │
│       ├── store/             # Redux state management
│       ├── utils/             # Utility functions
│       ├── hooks/             # Custom React hooks
│       ├── types/             # TypeScript type definitions
│       ├── themes/            # Theme configurations
│       └── App.tsx           # Application root component

├── docs/                     # Project documentation
├── scripts/                  # Automation scripts
├── .gitignore               # Git ignore rules
├── docker-compose.yml       # Development environment
├── README.md                # Project overview
└── CHANGELOG.md             # Version history
