# Fundamental Principles
- Use BRITISH English for all code and documentation.
- Always write clean, simple, modular, and functional code.
- Focus on core functionality before optimisation
- Think thoroughly before coding. Write 2-3 reasoning paragraphs.
- Use clear and consistent naming.
- Always use full package paths for imports (e.g., `from backend.apps.fields.services.optimization` instead of relative imports like `from ..services.optimization`)

# Error Fixing
- DO NOT jump to conclusions! Consider multiple possible causes before deciding.
- Explain the problem in plain English.
- Make minimal neccessary changes, change as few lines of code as possible.
- In case of a strage error, provide the user with a websearch prompt and ask to use perplexity to find a solution.

# Comments
- Add helpful comments in the code.
- NEVER delete comments, if they are not obsolete or obviously wrong.
- Include reasoning in the comments.

----

## Context
You are assisting in developing "CropCompass", a Django-based web application designed to help farmers optimize crop yields and efficiently manage their fields using geospatial data.

## Technical Stack
- **Backend**: Django (GeoDjango), Django REST Framework, PostgreSQL with PostGIS
- **Frontend**: React.js, Redux Toolkit, styled-components
- **Authentication**: JWT (django-rest-framework-simplejwt)
- **Geospatial Tools**: GeoDjango, PostGIS, GDAL, QGIS, Leaflet
- **Mobile Features**: Service Worker for offline support, responsive design

## Ticket Numbering
- Use prefix 'dev-' followed by incrementing numbers starting from 100
- Example: dev-100, dev-101, dev-102, etc.

## Coding Standards & Best Practices
- Use BRITISH English for all code and documentation.
- Follow Python PEP 8 guidelines.
- Strictly Follow Django's recommended project structure and conventions.
- Use clear, descriptive naming conventions for variables, functions, classes, and files.
- Ensure frontend components follow React best practices and Airbnb JavaScript style guide.
- Implement responsive design optimized for mobile and tablet devices.
- Ensure all API endpoints follow RESTful principles.
- Use functional components and hooks for React code.
- Use transient props with the $ prefix in styled-components.
- Preserve unrelated code when modifying code, it is essential to ensure that unrelated code remains intact.
- Focus on the Task Scope.
- Avoid Over-Optimisation.
- Comment Before Modifying.
- Ensure that only the code relevant to the current task is modified while preserving all unrelated code.

## Database & Geospatial Operations
- Use Django's ORM exclusively for database interactions.
- All geospatial operations must leverage GeoDjango and PostGIS functionalities.

## Authentication & Security
- Implement JWT-based authentication using `django-rest-framework-simplejwt`.
- Enforce role-based access control based on subscription tiers (Basic, Standard, Premium).
- Follow Django security best practices (e.g., protection against SQL injection, XSS).

## Performance & Optimization
- Write efficient database queries; avoid N+1 query problems.
- Implement caching strategies where applicable.
- Ensure frontend assets are optimized for performance (lazy loading, code splitting).
- Utilize service workers for offline-first capabilities.

## Error Handling & Logging
- Implement clear and consistent error handling across backend APIs.
- Log important events and errors using Django's logging framework.
- Include network status indicators for offline states.

## Testing Requirements
- Write unit tests for all backend logic (models, views, serializers).
- Include integration tests for API endpoints.
- Aim for at least 80% test coverage.
- Test offline functionality in various network conditions.

## Documentation Requirements
- Include clear docstrings for all backend classes and functions.
- Maintain README.md files describing setup instructions clearly.
- Document offline capabilities and local storage strategies.

## Output Guidelines
When providing code snippets:
- Clearly comment your code to explain logic where necessary.
- Provide concise explanations alongside code samples when requested or appropriate.
- Always adhere strictly to the technical stack and standards outlined above.
When resolving errors:
- Evaluate 3 reasons for an error, don't jump to conclusions.


----

## Instruction Files
[Product Requirements Document.md]
[User Interface Design Document.md]
[Software Requirements Specification.md]

----

# Fundamental Principles
- Use BRITISH English for all code and documentation.
- Always write clean, simple, modular, and functional code.
- Focus on core functionality before optimisation
- Think thoroughly before coding. Write 2-3 reasoning paragraphs.
- Use clear and consistent naming.
- Always use full package paths for imports (e.g., `from backend.apps.fields.services.optimization` instead of relative imports like `from ..services.optimization`)
- Focus on the Task Scope
- Avoid Over-Optimisation
- Ensure that only the code relevant to the current task is modified while preserving all unrelated code.
- Comment Before Modifying.
- Strive to provide precise and knowledgeable responses that demonstrate proper understanding of fundamental programming concepts.

# Error Fixing
- DO NOT jump to conclusions! Consider multiple possible causes before deciding.
- Explain the problem in plain English.
- Make minimal neccessary changes, change as few lines of code as possible.
- In case of a strage error, provide the user with a websearch prompt and ask to use perplexity to find a solution.

# Comments
- Add helpful comments in the code.
- NEVER delete comments, if they are not obsolete or obviously wrong.
- Include reasoning in the comments.
