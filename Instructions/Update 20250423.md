UKAMA CropCompass Update 23/04/2025

- [ ] recording
    - [x] change overwrite protection when there was a continuous point
    - [x] automatic corner, boundary detection, add marker to the corner points
    - [x] back to start notification and option to "magnetic" close polygon
    - [x] discard additional points from continuous recording past starting point
    - [x] simplify mark spot, we remove the point type from the view, and only use the tag and notes when recording, they are automatically assigned to type mark
        - we keep the types boundary, mark, track
    - [x] When setting the compass view you should be able to edit the custom direction degrees manually not only through the slider
- [ ] map
    - [x] add a a dot to the map for current position, since we have the GPS location already, even without recording. this will help with centre map without recorded data.
    - [x] add remove point to the point edit sheet
    - [x] centre mao to current location, if there is no point recorded yet
- [ ] Settings
    - [x] remove map style, it's already in mapview
    - [x] move gpd settings to the top
    - [x] rename measurement system to system, and change Font colour to white
