# UI Test Debugging Cycle (April 2025)

## Initial Problem:
UI tests (`CropCompass_GPSUITests`) started failing intermittently after adding accessibility identifiers. Failures manifested as:
1.  Inability to verify GPS signal (stuck on "No Signal").
2.  Inability to find elements within presented sheets (e.g., farm/field management, add farm/field).

## GPS "No Signal" Issue Investigation & Fixes:

*   **Symptom:** The `verifyGPSSimulation` check consistently failed, with the `indicator.gps.status` label never updating from its initial "No Signal" state.
*   **Root Cause Analysis:**
    *   Confirmed `LocationManager.signalStrength` relies on `locationManager(_:didUpdateLocations:)` being called.
    *   Eliminated test timing (`XCTNSPredicateExpectation` fixed the *check* but not the underlying problem).
    *   Identified a critical typo (`rivacy` vs `Privacy`) and incorrect keys for location usage descriptions in `Info.plist`. Without the correct `NSLocationWhenInUseUsageDescription` key, authorisation requests failed silently.
    *   Identified a race condition: `startUpdatingLocation()` was called in `LocationManager.init()` *before* asynchronous authorisation could complete.
*   **Resolution:**
    1.  Corrected `Info.plist` keys to `NSLocationWhenInUseUsageDescription` and `NSLocationAlwaysAndWhenInUseUsageDescription`.
    2.  Removed `startUpdatingLocation()` calls from `LocationManager` initializers.
    3.  Relied on `ContentView.onAppear` and `locationManager(_:didChangeAuthorization:)` to start updates *after* authorisation is confirmed.
*   **Learning:** Ensure correct `Info.plist` keys for location usage. Avoid starting location updates until authorisation status is confirmed (e.g., not directly in `init`).

## Sheet Finding Issue Investigation & Fixes:

*   **Symptom:** Tests failed to find presented sheets using queries like `app.sheets["sheet.identifier"]`.
*   **Root Cause Analysis:** Examined the relevant views (`FarmFieldManagementView`, `AddFarmView`, `AddFieldView`) and found that the `.accessibilityIdentifier("sheet.*")` was consistently applied to the root `NavigationView` *within* the sheet's content, not the system sheet container itself.
*   **UI Test Query Mechanism:** UI tests typically represent `NavigationView` elements as `XCUIElementType.other`.
*   **Resolution:** Changed all sheet queries where the identifier was on a `NavigationView` to use `app.otherElements["sheet.identifier"]` instead of `app.sheets["sheet.identifier"]`. This needed to be applied consistently to the initial sheet (`sheet.farmfield.management`) and nested sheets (`sheet.farm`, `sheet.field`).
*   **Learning:** When a SwiftUI `.sheet` presents content whose root is a `NavigationView` with an accessibility identifier, UI tests must query for it using `app.otherElements["identifier"]`, not `app.sheets["identifier"]`.

*   **Sheet Dismissal Verification Issue (RECURRENCE & FINAL FIX):**
    *   **Symptom:** After fixing the dismissal check for the *Add Farm* sheet, the test failed again, this time waiting for the *Add Field* sheet to disappear after saving.
    *   **Root Cause Analysis:** The robust dismissal check (waiting for an element in the parent view to become active) was only applied to the Add Farm dismissal, not the Add Field dismissal. The Add Field check still used the brittle `waitForElementToDisappear`.
    *   **Resolution:** Applied the *same* robust dismissal logic to the Add Field step. After tapping the Save Field button, the test now waits for the `Done` button (`button.done`) in the parent `farmManagementView` to become hittable, confirming the context has returned. **Update (05-Apr-2025):** This robust pattern was also applied to the final dismissal of the main `Farm Management` sheet itself (after tapping its 'Done' button), by waiting for the 'Manage Farms' button in the underlying `Record` view to become hittable.
    *   **Learning:** Apply fixes and learnings consistently across all similar interaction patterns within the test suite.

---

## Basic Navigation Issues (testBasicNavigation):

*   **Symptom:** Test failed to find main views (`RecordingView`, `MapView`, `ExportView`, `SettingsView`) after tapping corresponding tab bar buttons.
*   **Root Cause Analysis 1 (View Identification):** Initial checks attempted to use `app.otherElements["view.identifier"]` based on accessibility identifiers applied in the view code. This worked for `ExportView` and `SettingsView`, but failed for `RecordingView` and `MapView` because the identifier wasn't present/detected on the root `NavigationView`.
*   **Resolution 1 (View Identification):** The most reliable method was found to be checking for the navigation bar by its title for all four views: `app.navigationBars["Record"]`, `app.navigationBars["Field Map"]`, `app.navigationBars["Export GPS Data"]`, and `app.navigationBars["Settings"]`. The test was updated accordingly.
*   **Root Cause Analysis 2 (Tab Bar Button Identification):** Test failed to find tab bar buttons using assigned identifiers (`tab.map`, etc.). Log analysis showed buttons were identified by their labels ("Map", "Export", etc.) instead.
*   **Resolution 2 (Tab Bar Button Identification):** Modified test to query tab bar buttons by their visible labels.
*   **Learning:** For views presented by a `TabView`, checking for the `NavigationView`'s title via `app.navigationBars["Title"]` appears more robust than relying on `app.otherElements["view.identifier"]`, even if the identifier is set on the `NavigationView`. Tab bar item identifiers might not be reliably detected by UI tests; querying by label is a viable fallback.

---

## Mark Spot Test Issues (testMarkSpot):

*   **Symptom 1 (Original):** GPS Signal verification failure (Resolved by Xcode Reset / `Info.plist` / `LocationManager` logic fixes).
*   **Symptom 2 (After GPS Fix):** Test failed to find the "Custom Note..." button within the tag selection menu after tapping the menu button (`menu.selecttag`). The query `app.buttons["tag.custom.note..."]` failed.
*   **Root Cause Analysis 2:** Querying dynamically generated `Menu` items by `.accessibilityIdentifier` in SwiftUI can be unreliable in UI Tests. The system often identifies them better by their visible label.
*   **Resolution 2:** Changed the query for the menu item from `app.buttons["tag.custom.note..."]` to `app.buttons["Custom Note..."]` (using the visible label).
*   **Learning:** For items within a SwiftUI `Menu`, querying by label (`app.buttons["Label Text"]`) is often more robust than relying on `.accessibilityIdentifier` assigned to the `Button` within the `Menu` structure.

---
