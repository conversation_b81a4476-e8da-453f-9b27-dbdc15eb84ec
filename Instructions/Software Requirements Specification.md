# Software Requirements Specification Document

## System Design
- **Frontend**: Interactive web application optimized for mobile and tablet devices, focusing on simplicity and usability.
- **Backend**: Django framework with GeoDjango for geospatial data processing and integration.
- **Database**: PostgreSQL with PostGIS extension for geospatial data storage and querying.

## Architecture Pattern
- **Monolithic Architecture**: Django's all-in-one framework is ideal for shared hosting environments, combining backend logic, database management, and API handling in a single application.
- **Client-Server Model**: Frontend communicates with the backend via RESTful APIs.

## State Management
- **Frontend State Management**: Use React's Context API or Redux for managing application states like user sessions, field data, and subscription details.
- **Backend State Management**: Stateless API endpoints; session management handled via Django's built-in authentication system.

## Data Flow
1. Farmers input GPS data, soil type, and crop details via the frontend.
2. Frontend sends requests to Django backend APIs for processing.
3. Backend processes data using geospatial queries (PostGIS) and algorithms to calculate recommendations.
4. Processed data is sent back to the frontend for visualization (dashboard or map view).

## Technical Stack
- **Frontend**: React.js (JavaScript), HTML5, CSS3.
- **Backend**: Django with GeoDjango for geospatial operations.
- **Database**: PostgreSQL with PostGIS extension.
- **Hosting**: Shared hosting environment supporting Python 3.11 and PostgreSQL.
- **Geospatial Tools**:
  - QGIS for local geospatial data management.
  - Google Maps API for GPS-based field visualization.
  - OpenWeather API for weather data integration.

## Authentication Process
- **Token-Based Authentication**:
  - User login handled by Django's built-in authentication system.
  - JWT tokens issued for session management via `django-rest-framework-simplejwt`.
- **Role-Based Access Control**:
  - Permissions defined based on subscription tiers (Basic, Standard, Premium).

## Route Design
1. **Frontend Routes**:
   - `/dashboard`: Displays key metrics and collapsible sections.
   - `/map`: Interactive map view with overlays for field-specific data.
   - `/reports`: Historical trends and yield comparisons.
   - `/settings`: Subscription management, dark mode toggle, and offline mode settings.

2. **Backend Endpoints**:
   - `POST /api/auth/login`: User authentication.
   - `POST /api/fields`: Submit GPS data and crop details.
   - `GET /api/recommendations`: Fetch calculated recommendations.
   - `GET /api/weather`: Retrieve weather forecasts.

## API Design
- **RESTful APIs**:
  - CRUD operations for managing user profiles, fields, subscriptions, and historical data.
  - Integration with external APIs (weather services, GPS mapping).
  - Secure endpoints protected by token-based authentication.

## Database Design ERD
| Entity           | Attributes                          | Relationships              |
|-------------------|-------------------------------------|----------------------------|
| Owner             | UserID, Name, Email, PasswordHash  | One-to-Many with Fields    |
| Fields            | FieldID, GPSData (PostGIS), SoilType | Many-to-One with Owner     |
| Recommendations   | RecID, FieldID, WaterReqs, IrrigationMethod | One-to-One with Fields |
| Subscriptions     | SubID, TierType, Features          | One-to-One with Owner      |
| HistoricalData    | HistID, FieldID, YieldTrends       | Many-to-One with Fields    |

## Dependencies
### Backend Dependencies:
1. **Django** (`5.1`): Core web framework supporting GeoDjango for geospatial operations.
2. **Django REST Framework** (`3.15`): For building RESTful APIs.
3. **PostgreSQL Database Adapter (psycopg)** (`3.2.6`): Enables Django to connect to PostgreSQL databases.
4. **GeoDjango/PostGIS Extension** (`PostGIS 3.5.0`): For advanced geospatial queries and spatial indexing in PostgreSQL.
5. **Plotly** (`6.0.1`): For creating interactive data visualizations.

### Frontend Dependencies:
1. **React.js** (`18.2.0`): For building dynamic user interfaces.
2. **Redux Toolkit** (`2.0`): For managing application state efficiently in React apps.
3. **Nivo** (`0.88.0`): For advanced data visualization components.
5. **Leaflet** (`1.9.4`): For interactive maps and GPS overlays.

### Authentication Dependencies:
1. **django-rest-framework-simplejwt** (`5.2.2`): Provides JWT-based authentication support.

### Geospatial Dependencies:
1. **GDAL** (`3.10.2`): Required by GeoDjango for geospatial operations (ensure compatibility with your hosting environment).
2. **Shapely** (`2.0.7`): Python library for manipulating geometric objects (optional but useful). Prioritise GeoDjango.
3. **Google Maps API SDKs**: For GPS-based field visualisation.

### Other Utilities:
1. **OpenWeather API SDKs**: For weather integration services.

----

Ensure all dependencies are installed using a Python 3.11 on Debian 6.1 virtual environment (`venv`) to avoid conflicts during development.
