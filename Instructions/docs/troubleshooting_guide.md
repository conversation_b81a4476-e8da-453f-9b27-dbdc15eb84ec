# Troubleshooting Guide

## Common Issues and Solutions

### Authentication Issues

#### JWT Token Invalid
**Symptom**: 401 Unauthorized errors with message "Token is invalid or expired"
**Solutions**:
1. Check token expiration time
2. Ensure correct token format in Authorization header
3. Verify clock synchronization between servers

```python
# Check token expiration
from jwt import decode
token_data = decode(token, verify=False)
print(token_data['exp'])
```

### Database Connectivity

#### PostGIS Operations Failing
**Symptom**: GeoDjango operations raise GDALException
**Solutions**:
1. Verify GDAL installation:
```bash
gdal-config --version
```
2. Check PostGIS extension:
```sql
SELECT PostGIS_version();
```
3. Ensure spatial_ref_sys table exists:
```sql
SELECT count(*) FROM spatial_ref_sys;
```

### Subscription Issues

#### Feature Access Denied
**Symptom**: 403 Forbidden when accessing premium features
**Debug Steps**:
1. Check current subscription:
```python
from apps.subscriptions.services import SubscriptionService
subscription = SubscriptionService.get_current_subscription(user)
print(subscription.features)
```
2. Verify permission classes:
```python
from apps.subscriptions.permissions import HasFeatureAccess
permission = HasFeatureAccess('feature_name')
print(permission.has_permission(request, view))
```

### Performance Issues

#### Slow Field Queries
**Symptom**: Field operations taking >1s to complete
**Solutions**:
1. Check spatial indices:
```sql
EXPLAIN ANALYZE SELECT * FROM fields WHERE...;
```
2. Optimize geometry columns:
```sql
CLUSTER fields USING field_geometry_idx;
```

## Logging and Debugging

### Enable Debug Logging
```python
# settings/local.py
LOGGING = {
    'version': 1,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

### Common Error Codes

- `ERR_SUB_001`: Subscription expired
- `ERR_GEO_001`: Invalid geometry data
- `ERR_AUTH_001`: Authentication failed
- `ERR_LIMIT_001`: Resource limit exceeded

## Support Resources

- Documentation: `/docs`
- API Status: `/api/health/`
- System Status: `/admin/status/`