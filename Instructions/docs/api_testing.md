# API Testing and Monitoring Guide

## Field Model Integration Tests

Comprehensive integration tests have been implemented for the Field Model component. These tests ensure that the Field Model correctly integrates with other components of the system, including the database, API endpoints, and external services.

For detailed information on the Field Model integration tests, see [Field Model Integration Tests](field_model_integration_tests.md).

## Enhanced Test Reporting

The project implements an enhanced test reporting system that provides detailed, interactive reports for test execution. This system includes:

- Interactive HTML reports with charts and visualizations
- Detailed coverage reports
- Summary reports in markdown format

For more information on the enhanced test reporting system, see [Enhanced Test Reporting](enhanced_test_reporting.md).

[Previous sections remain the same...]

## Monitoring Guidelines

### Health Checks
```python
# monitoring/health_checks.py
from django.db import connections
from django.db.utils import OperationalError
from redis import Redis
from django.conf import settings

def check_database():
    try:
        for name in connections.databases:
            cursor = connections[name].cursor()
            cursor.execute("SELECT 1")
            row = cursor.fetchone()
            if row is None:
                return False
        return True
    except OperationalError:
        return False

def check_redis():
    try:
        redis_client = Redis.from_url(settings.REDIS_URL)
        return redis_client.ping()
    except:
        return False

def check_postgis():
    try:
        cursor = connections['default'].cursor()
        cursor.execute("SELECT PostGIS_Version()")
        return True
    except:
        return False
```

### Performance Monitoring

#### Prometheus Metrics
```python
# monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge
from django.conf import settings

# Request metrics
REQUEST_LATENCY = Histogram(
    'http_request_latency_seconds',
    'Request latency in seconds',
    ['method', 'endpoint']
)

REQUEST_COUNT = Counter(
    'http_request_count_total',
    'Total request count',
    ['method', 'endpoint', 'status']
)

# Resource metrics
ACTIVE_USERS = Gauge(
    'active_users_total',
    'Number of active users'
)

DB_CONNECTION_POOL = Gauge(
    'db_connection_pool_size',
    'Database connection pool size'
)

# Business metrics
FIELD_CREATION_COUNT = Counter(
    'field_creation_total',
    'Total number of fields created',
    ['subscription_tier']
)
```

#### Middleware for Request Tracking
```python
# monitoring/middleware.py
import time
from .metrics import REQUEST_LATENCY, REQUEST_COUNT

class MetricsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()

        response = self.get_response(request)

        latency = time.time() - start_time
        REQUEST_LATENCY.labels(
            method=request.method,
            endpoint=request.path
        ).observe(latency)

        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.path,
            status=response.status_code
        ).inc()

        return response
```

### Logging Configuration
```python
# settings/production.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'fmt': '%(levelname)s %(asctime)s %(request_id)s %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'json'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/api.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'json'
        }
    },
    'loggers': {
        'api': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True
        }
    }
}
```

### Alert Configuration
```python
# monitoring/alerts.py
from django.core.mail import send_mail
from django.conf import settings

ALERT_THRESHOLDS = {
    'response_time': 1.0,  # seconds
    'error_rate': 0.05,    # 5%
    'cpu_usage': 0.80,     # 80%
    'memory_usage': 0.85,  # 85%
    'disk_usage': 0.90     # 90%
}

def send_alert(alert_type, message):
    send_mail(
        subject=f'Alert: {alert_type}',
        message=message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=settings.ALERT_EMAILS
    )
```

## Scaling Guidelines

### Load Testing Configuration
```python
# tests/load_testing/locustfile.py
from locust import HttpUser, task, between

class APIUser(HttpUser):
    wait_time = between(1, 3)

    def on_start(self):
        # Login and store token
        response = self.client.post("/api/auth/login/", {
            "username": "test_user",
            "password": "test_pass"
        })
        self.token = response.json()["access"]
        self.client.headers = {"Authorization": f"Bearer {self.token}"}

    @task(3)
    def get_fields(self):
        self.client.get("/api/fields/")

    @task(1)
    def create_field(self):
        self.client.post("/api/fields/", {
            "name": "Test Field",
            "area": 10.5,
            "coordinates": {
                "type": "Polygon",
                "coordinates": [[[0, 0], [1, 1], [0, 1], [0, 0]]]
            }
        })
```

### Caching Strategy
```python
# cache/strategies.py
from django.core.cache import cache
from django.conf import settings

class CacheStrategy:
    @staticmethod
    def get_field_key(field_id):
        return f"field:{field_id}"

    @staticmethod
    def get_weather_key(field_id):
        return f"weather:{field_id}"

    @staticmethod
    def cache_field(field):
        key = CacheStrategy.get_field_key(field.id)
        cache.set(key, field, timeout=settings.FIELD_CACHE_TTL)

    @staticmethod
    def invalidate_field(field_id):
        cache.delete(CacheStrategy.get_field_key(field_id))
        cache.delete(CacheStrategy.get_weather_key(field_id))
```

### Database Optimization
```python
# settings/production.py
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': 'cropcompass',
        'CONN_MAX_AGE': 60,
        'OPTIONS': {
            'MAX_CONNS': 100,
            'CONN_HEALTH_CHECKS': True,
        },
    }
}

# Example query optimization
from django.db.models import Prefetch

def get_user_fields(user):
    return Field.objects.filter(user=user).prefetch_related(
        Prefetch('crops', queryset=Crop.objects.select_related('type')),
        'soil_tests'
    ).select_related('subscription')
```

### Horizontal Scaling Configuration
```python
# settings/production.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis-cluster:6379/0',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'MASTER_CACHE': True,
        }
    }
}

# Celery configuration for distributed tasks
CELERY_BROKER_URL = 'redis://redis-cluster:6379/1'
CELERY_RESULT_BACKEND = 'redis://redis-cluster:6379/2'
CELERY_TASK_DEFAULT_QUEUE = 'default'
CELERY_TASK_QUEUES = {
    'default': {},
    'high-priority': {
        'queue_type': 'priority',
        'priority': 10,
    },
    'low-priority': {
        'queue_type': 'priority',
        'priority': 1,
    }
}
```

### Performance Testing Thresholds
```python
# tests/performance/thresholds.py
PERFORMANCE_THRESHOLDS = {
    'api_response_time': {
        'p95': 500,  # milliseconds
        'p99': 1000  # milliseconds
    },
    'database_query_time': {
        'p95': 100,  # milliseconds
        'p99': 200   # milliseconds
    },
    'concurrent_users': {
        'sustained': 1000,
        'peak': 2000
    },
    'requests_per_second': {
        'sustained': 50,
        'peak': 100
    }
}
```

## Disaster Recovery Procedures

### Backup Strategy
```python
# backup/manager.py
from django.core.management.base import BaseCommand
from django.conf import settings
import boto3
import subprocess
from datetime import datetime

class BackupManager:
    def __init__(self):
        self.s3 = boto3.client('s3')
        self.backup_bucket = settings.BACKUP_BUCKET

    def create_database_backup(self):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'db_backup_{timestamp}.dump'

        # Create PostgreSQL backup
        subprocess.run([
            'pg_dump',
            '-Fc',  # Custom format
            '-Z9',  # Maximum compression
            '-f', f'/tmp/{filename}',
            settings.DATABASES['default']['NAME']
        ])

        # Upload to S3
        self.s3.upload_file(
            f'/tmp/{filename}',
            self.backup_bucket,
            f'database/{filename}'
        )

    def backup_media_files(self):
        for root, _, files in os.walk(settings.MEDIA_ROOT):
            for file in files:
                local_path = os.path.join(root, file)
                s3_path = f'media/{os.path.relpath(local_path, settings.MEDIA_ROOT)}'
                self.s3.upload_file(local_path, self.backup_bucket, s3_path)
```

### Recovery Procedures
```python
# recovery/procedures.py
class DisasterRecovery:
    @staticmethod
    def restore_database(backup_file):
        subprocess.run([
            'pg_restore',
            '--clean',  # Clean (drop) database objects before recreating
            '--if-exists',
            '-d', settings.DATABASES['default']['NAME'],
            backup_file
        ])

    @staticmethod
    def verify_data_integrity():
        checks = [
            'SELECT count(*) FROM auth_user',
            'SELECT count(*) FROM fields_field',
            'SELECT PostGIS_Version()',
        ]

        with connections['default'].cursor() as cursor:
            for check in checks:
                cursor.execute(check)
                if not cursor.fetchone():
                    raise DataIntegrityError(f"Integrity check failed: {check}")

    @staticmethod
    async def failover_to_replica():
        """Execute failover to replica database"""
        await asyncio.gather(
            stop_write_operations(),
            verify_replica_sync(),
            switch_database_connection()
        )
```

### Recovery Time Objectives (RTO)
```python
# recovery/sla.py
RECOVERY_OBJECTIVES = {
    'critical_systems': {
        'rto': timedelta(hours=1),    # Recovery Time Objective
        'rpo': timedelta(minutes=15),  # Recovery Point Objective
        'services': ['database', 'api', 'auth']
    },
    'non_critical_systems': {
        'rto': timedelta(hours=4),
        'rpo': timedelta(hours=1),
        'services': ['reporting', 'analytics']
    }
}
```

## Security Testing Guidelines

### Authentication Testing
```python
# tests/security/test_auth.py
from django.test import TestCase
from rest_framework.test import APIClient
from django.contrib.auth.models import User
import jwt

class AuthenticationTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='ComplexPass123!'
        )

    def test_password_complexity(self):
        weak_passwords = [
            'password123',
            'qwerty',
            'testuser123'
        ]
        for password in weak_passwords:
            response = self.client.post('/api/auth/register/', {
                'username': 'newuser',
                'password': password
            })
            self.assertEqual(response.status_code, 400)

    def test_jwt_token_expiry(self):
        response = self.client.post('/api/auth/token/', {
            'username': 'testuser',
            'password': 'ComplexPass123!'
        })
        token = response.data['access']
        decoded = jwt.decode(token, options={"verify_signature": False})
        self.assertLess(decoded['exp'] - decoded['iat'], 3600)  # 1 hour max
```

### Authorization Testing
```python
# tests/security/test_authorization.py
class AuthorizationTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(username='testuser')
        self.admin = User.objects.create_superuser(username='admin')

    def test_field_access_control(self):
        # Create a field owned by testuser
        field = Field.objects.create(owner=self.user)

        # Test access with different users
        test_cases = [
            (None, 401),  # Unauthenticated
            (self.user, 200),  # Owner
            (self.admin, 200),  # Admin
            (User.objects.create_user(username='other'), 403)  # Other user
        ]

        for user, expected_status in test_cases:
            self.client.force_authenticate(user=user)
            response = self.client.get(f'/api/fields/{field.id}/')
            self.assertEqual(response.status_code, expected_status)
```

### Penetration Testing Scenarios
```python
# tests/security/test_penetration.py
class PenetrationTests(TestCase):
    def test_sql_injection(self):
        injection_attempts = [
            "'; DROP TABLE users--",
            "' UNION SELECT username, password FROM users--",
            "' OR '1'='1"
        ]

        for attempt in injection_attempts:
            response = self.client.get(f'/api/fields/?name={attempt}')
            self.assertNotIn('error', response.data.lower())

    def test_xss_prevention(self):
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src='x' onerror='alert(1)'>"
        ]

        for payload in xss_payloads:
            response = self.client.post('/api/fields/', {
                'name': payload,
                'description': payload
            })
            self.assertEqual(response.status_code, 400)
```

### Security Headers Testing
```python
# tests/security/test_headers.py
class SecurityHeadersTests(TestCase):
    def test_security_headers(self):
        response = self.client.get('/')
        headers = response.headers

        self.assertEqual(
            headers['Strict-Transport-Security'],
            'max-age=31536000; includeSubDomains'
        )
        self.assertEqual(headers['X-Content-Type-Options'], 'nosniff')
        self.assertEqual(headers['X-Frame-Options'], 'DENY')
        self.assertIn('Content-Security-Policy', headers)
```

### Rate Limiting Tests
```python
# tests/security/test_rate_limiting.py
class RateLimitTests(TestCase):
    def test_login_rate_limit(self):
        for _ in range(5):
            self.client.post('/api/auth/login/', {
                'username': 'testuser',
                'password': 'wrong'
            })

        response = self.client.post('/api/auth/login/', {
            'username': 'testuser',
            'password': 'wrong'
        })
        self.assertEqual(response.status_code, 429)
```

## Compliance Testing

### GDPR Compliance Tests
```python
# tests/compliance/test_gdpr.py
from django.test import TestCase
from django.core import mail
from datetime import datetime, timedelta

class GDPRComplianceTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='ComplexPass123!'
        )
        self.client.force_authenticate(user=self.user)

    def test_data_export(self):
        """Test user's right to data portability"""
        response = self.client.get('/api/users/me/export-data/')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        required_fields = ['personal_data', 'fields', 'usage_history']
        for field in required_fields:
            self.assertIn(field, data)

    def test_data_deletion(self):
        """Test user's right to be forgotten"""
        response = self.client.post('/api/users/me/delete-account/')
        self.assertEqual(response.status_code, 200)

        # Verify cascade deletion
        self.assertFalse(User.objects.filter(id=self.user.id).exists())
        self.assertFalse(Field.objects.filter(owner=self.user).exists())
        self.assertFalse(UserPreference.objects.filter(user=self.user).exists())

    def test_consent_management(self):
        """Test consent recording and withdrawal"""
        # Give consent
        response = self.client.post('/api/consent/', {
            'marketing_emails': True,
            'data_analytics': True
        })
        self.assertEqual(response.status_code, 200)

        # Withdraw consent
        response = self.client.delete('/api/consent/marketing_emails/')
        self.assertEqual(response.status_code, 200)

        # Verify consent status
        response = self.client.get('/api/consent/')
        self.assertEqual(response.json()['marketing_emails'], False)
        self.assertEqual(response.json()['data_analytics'], True)

    def test_data_retention(self):
        """Test data retention policies"""
        # Create test data
        old_date = datetime.now() - timedelta(days=400)
        FieldHistory.objects.create(
            field_id=1,
            action='update',
            timestamp=old_date
        )

        # Run retention policy check
        management.call_command('cleanup_old_data')

        # Verify old data is removed
        self.assertFalse(
            FieldHistory.objects.filter(timestamp__lte=old_date).exists()
        )

    def test_privacy_notice(self):
        """Test privacy notice version tracking"""
        response = self.client.get('/api/privacy-notice/latest/')
        current_version = response.json()['version']

        # Verify user acknowledgment
        response = self.client.post('/api/privacy-notice/acknowledge/', {
            'version': current_version
        })
        self.assertEqual(response.status_code, 200)
```

### Data Processing Agreement Tests
```python
# tests/compliance/test_dpa.py
class DataProcessingTests(TestCase):
    def test_third_party_data_handling(self):
        """Test third-party data processing compliance"""
        # Test weather data processing
        response = self.client.get('/api/weather/forecast/')
        self.assertIn('data_processing_notice', response.json())

        # Test external API data handling
        response = self.client.get('/api/satellite/imagery/')
        self.assertIn('data_processor_details', response.json())

    def test_data_minimization(self):
        """Test principle of data minimization"""
        response = self.client.get('/api/fields/1/')
        data = response.json()

        # Verify only necessary data is returned
        sensitive_fields = ['exact_coordinates', 'soil_analysis_raw']
        for field in sensitive_fields:
            self.assertNotIn(field, data)
```

## Incident Response Procedures

### Security Incident Response
```python
# security/incident_response.py
from django.core.mail import send_mail
from django.conf import settings
import logging

logger = logging.getLogger('security')

class IncidentResponse:
    SEVERITY_LEVELS = {
        'LOW': 1,
        'MEDIUM': 2,
        'HIGH': 3,
        'CRITICAL': 4
    }

    def __init__(self):
        self.incident_handlers = {
            'unauthorized_access': self.handle_unauthorized_access,
            'data_breach': self.handle_data_breach,
            'ddos_attack': self.handle_ddos,
            'malware_detection': self.handle_malware
        }

    async def handle_incident(self, incident_type, details):
        """Main incident handler"""
        logger.critical(f"Security incident: {incident_type}")

        # Execute immediate response
        handler = self.incident_handlers.get(incident_type)
        if handler:
            await handler(details)

        # Notify security team
        self.notify_security_team(incident_type, details)

        # Create incident report
        self.create_incident_report(incident_type, details)

    async def handle_unauthorized_access(self, details):
        """Handle unauthorized access attempts"""
        # Block suspicious IP
        await self.block_ip(details['ip_address'])

        # Invalidate affected sessions
        await self.invalidate_sessions(details['affected_users'])

        # Enable enhanced monitoring
        self.enable_enhanced_monitoring()

    async def handle_data_breach(self, details):
        """Handle potential data breaches"""
        # Stop affected services
        await self.stop_affected_services(details['affected_services'])

        # Start breach assessment
        breach_scope = await self.assess_breach_scope(details)

        # Notify affected users
        if breach_scope['requires_notification']:
            self.notify_affected_users(breach_scope['affected_users'])

        # Prepare GDPR breach notification
        if breach_scope['requires_gdpr_notification']:
            self.prepare_gdpr_breach_notification(breach_scope)

    def notify_security_team(self, incident_type, details):
        """Notify security team about the incident"""
        subject = f"SECURITY INCIDENT: {incident_type}"
        message = self.format_incident_message(incident_type, details)

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.SECURITY_EMAIL,
            recipient_list=settings.SECURITY_TEAM_EMAILS,
            fail_silently=False
        )

    def create_incident_report(self, incident_type, details):
        """Create detailed incident report"""
        report = SecurityIncidentReport.objects.create(
            incident_type=incident_type,
            severity=self.assess_severity(incident_type, details),
            details=details,
            initial_response=self.get_initial_response_details(),
            timestamp=timezone.now()
        )
        return report

    @staticmethod
    def assess_severity(incident_type, details):
        """Assess incident severity"""
        if 'data_breach' in incident_type:
            return 'CRITICAL'
        if 'unauthorized_access' in incident_type:
            return 'HIGH'
        if 'suspicious_activity' in incident_type:
            return 'MEDIUM'
        return 'LOW'

class IncidentRecovery:
    async def execute_recovery_plan(self, incident_report):
        """Execute recovery plan based on incident type"""
        recovery_steps = self.generate_recovery_steps(incident_report)

        for step in recovery_steps:
            try:
                await step['function'](**step['params'])
                self.log_recovery_step(incident_report, step, success=True)
            except Exception as e:
                self.log_recovery_step(incident_report, step, success=False, error=str(e))
                raise

    def generate_recovery_steps(self, incident_report):
        """Generate recovery steps based on incident type"""
        if incident_report.incident_type == 'data_breach':
            return [
                {
                    'function': self.reset_affected_credentials,
                    'params': {'affected_users': incident_report.details['affected_users']}
                },
                {
                    'function': self.scan_for_vulnerabilities,
                    'params': {'systems': incident_report.details['affected_systems']}
                },
                {
                    'function': self.restore_from_backup,
                    'params': {'timestamp': incident_report.timestamp}
                }
            ]
        # Add more incident types as needed
        return []
