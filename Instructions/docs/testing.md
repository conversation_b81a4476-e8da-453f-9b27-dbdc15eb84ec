# Testing Guide

## Overview
This document describes the testing infrastructure and procedures for the Field Management System.

## Test Structure
- Unit Tests: Individual component testing
- Integration Tests: Component interaction testing
- Performance Tests: System performance validation

## Running Tests

### Local Development
```bash
# Run all tests
./run_tests.sh

# Run with coverage
./run_tests.sh --with-coverage

# Run specific test categories
pytest -m "not slow"  # Skip slow tests
pytest -m "integration"  # Run only integration tests
```

### Test Execution Process

The `run_tests.sh` script performs the following steps:

1. Activates the virtual environment
2. Sets the correct Django settings module
3. Creates necessary migrations if needed
4. Applies migrations to the test database
5. Runs the tests with the specified markers
6. Generates HTML and coverage reports
7. Outputs the location of the test reports

### Test Coverage
Current test coverage metrics:
- Models: 90%
- API Endpoints: 85%
- Integration Points: 95%
- Overall Coverage: 90%

### Key Test Areas
1. Field Model Integration
   - GeoDjango operations
   - Weather data integration
   - Equipment scheduling
   - Soil analysis
   - Bulk operations

   For detailed information on Field Model integration tests, see [Field Model Integration Tests](field_model_integration_tests.md).

2. Performance Benchmarks
   - Field creation: < 1s
   - Optimization calculations: < 0.5s
   - Bulk operations: Linear scaling

## CI/CD Integration
Tests are automatically run on:
- Pull requests to main/develop
- Direct pushes to main/develop
- Nightly builds

## Test Reports
Reports are generated in multiple formats:

### HTML Reports
The enhanced HTML reports include:
- Interactive charts showing test results distribution
- Detailed environment information
- Color-coded test results by status
- Performance metrics and timing information
- Formatted duration display (microseconds to hours)
- Summary statistics and pass rate calculation
- Tabbed interface for viewing different test categories

### Other Report Formats
- Markdown: Quick summary for documentation
- Coverage: HTML and XML reports with line-by-line coverage information
- JUnit XML: CI integration for automated build pipelines

Reports are available in the `/Users/<USER>/Coding/Project_CC/backend/test_reports/` directory after test execution.

### Custom Reporting Plugin
The test suite uses a custom pytest plugin (`pytest_field_plugin.py`) that enhances the standard pytest reporting with:
- Field-specific test categorization
- Enhanced HTML formatting
- Integration with the project's styling
- Additional metadata collection
- Performance metrics