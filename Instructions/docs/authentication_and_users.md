# Authentication and User Management

## Overview
CropCompass implements a JWT-based authentication system using `django-rest-framework-simplejwt`. The system supports role-based access control tied to subscription tiers.

## Authentication Flow

### 1. User Registration
```python
POST /api/auth/register/
{
    "username": "farmer_john",
    "email": "<EMAIL>",
    "password": "secure_password",
    "company_name": "Green Acres",
    "total_area": 150.5
}
```

### 2. User Login
```python
POST /api/auth/login/
{
    "username": "farmer_john",
    "password": "secure_password"
}

Response:
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGc...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbG..."
}
```

### 3. Token Refresh
```python
POST /api/auth/token/refresh/
{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbG..."
}
```

## User Model
The custom user model extends Django's `AbstractUser` with additional fields for farm management:

- `subscription_tier`: Current subscription level
- `subscription_expiry`: Subscription end date
- `company_name`: Farm or company name
- `total_area`: Total farm area in hectares

## API Endpoints

### User Management
- `GET /api/users/profile/`: Get current user profile
- `PATCH /api/users/profile/`: Update user profile
- `POST /api/auth/password/reset/`: Password reset request
- `POST /api/auth/password/reset/confirm/`: Confirm password reset

## Security Measures
- CSRF protection enabled
- JWT tokens with short expiry (15 minutes)
- Refresh tokens for extended sessions
- Password validation rules enforced