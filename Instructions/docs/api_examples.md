# API Examples with curl Commands

## Field Management Examples

### Create Field with Subdivisions
```bash
# Create main field
curl -X POST \
  'https://api.cropcompass.com/api/fields/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "West Field",
    "geometry": {
        "type": "Polygon",
        "coordinates": [[[30.0, 10.0], [40.0, 40.0], [20.0, 40.0], [30.0, 10.0]]]
    },
    "soil_type": "clay_loam"
  }'

# Create subdivision
curl -X POST \
  'https://api.cropcompass.com/api/fields/123/subdivisions/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Zone A",
    "geometry": {
        "type": "Polygon",
        "coordinates": [...]
    },
    "purpose": "CROP"
  }'
```

### Record Crop Rotation
```bash
curl -X POST \
  'https://api.cropcompass.com/api/fields/123/rotations/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "crop_id": 456,
    "start_date": "2024-03-15",
    "end_date": "2024-09-15",
    "is_planned": true
  }'
```

### Record Pest Incident
```bash
curl -X POST \
  'https://api.cropcompass.com/api/fields/123/pest-incidents/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "Aphid Infestation",
    "type": "PEST",
    "severity": 3,
    "detection_date": "2024-04-01"
  }'
```

### Get Field Recommendations
```http
GET /api/fields/1/recommendations
Authorization: Bearer <token>

Response 200:
{
    "irrigation": {
        "method": "drip",
        "schedule": ["2024-01-21T08:00:00Z", "2024-01-24T08:00:00Z"],
        "water_amount": 2.5
    },
    "planting": {
        "row_spacing": 30,
        "plant_spacing": 15,
        "orientation": "N-S"
    },
    "costs": {
        "estimated_per_hectare": 1200.50,
        "currency": "USD"
    }
}
```

## Weather Integration

### Get Weather Forecast
```http
GET /api/weather/forecast?field_id=1
Authorization: Bearer <token>

Response 200:
{
    "daily": [
        {
            "date": "2024-01-21",
            "temperature": {"min": 15, "max": 28},
            "precipitation": 0.5,
            "humidity": 65
        }
    ],
    "alerts": [
        {
            "type": "frost_warning",
            "start_time": "2024-01-22T00:00:00Z",
            "end_time": "2024-01-22T08:00:00Z"
        }
    ]
}
```

## Historical Data Analysis

### Get Yield History
```http
GET /api/fields/1/history
Authorization: Bearer <token>

Response 200:
{
    "yields": [
        {
            "year": 2023,
            "crop": "wheat",
            "yield_amount": 4.5,
            "unit": "tonnes_per_hectare"
        }
    ],
    "trends": {
        "average_yield": 4.2,
        "year_over_year_change": 0.3
    }
}
```

## Field Analysis Examples

### Get Pest Analysis
```bash
curl -X GET \
  'https://api.cropcompass.com/api/fields/123/analysis/pests/?start_date=2024-01-01&end_date=2024-03-23&include_weather=true' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Get Irrigation Analysis
```bash
curl -X GET \
  'https://api.cropcompass.com/api/fields/123/analysis/irrigation/?start_date=2024-01-01&end_date=2024-03-23' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Get Equipment Analysis
```bash
curl -X GET \
  'https://api.cropcompass.com/api/fields/123/analysis/equipment/?start_date=2024-01-01&end_date=2024-03-23&equipment_type=tractor' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Get Crop Performance Analysis
```bash
curl -X GET \
  'https://api.cropcompass.com/api/fields/123/analysis/crops/?start_date=2024-01-01&end_date=2024-03-23&crop_type=wheat' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

## Field Report Examples

### Generate Activity Log Report
```bash
curl -X POST \
  'https://api.cropcompass.com/api/fields/123/reports/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "report_type": "activity_log",
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "format": "pdf"
  }'
```

### Generate Compliance Report
```bash
curl -X POST \
  'https://api.cropcompass.com/api/fields/123/reports/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "report_type": "compliance",
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "format": "xlsx"
  }'
```
