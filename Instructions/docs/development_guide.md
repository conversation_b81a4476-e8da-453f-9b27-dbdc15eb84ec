# Development Guide

## Project Setup

### Prerequisites
- Python 3.13+
- PostgreSQL 15+ with PostGIS
- GDAL library
- Node.js 18+ (for frontend)

### Dependencies
The following major dependencies are required:
- Django 5.1 with GeoDjango for spatial operations
- scikit-learn 1.3.0+ for machine learning operations
- GDAL for geospatial processing
- PostgreSQL with PostGIS

### Machine Learning Components
The system uses scikit-learn for:
- Pest risk assessment and prediction
- Yield forecasting
- Weather pattern analysis

### Installation Notes
Ensure all ML dependencies are installed:
```bash
pip install 'scikit-learn>=1.3.0'
```

### Environment Setup
1. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Unix
venv\Scripts\activate     # Windows
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment variables:
```bash
cp .env.example .env
```

### Database Setup
1. Create PostgreSQL database with PostGIS extension
2. Update database settings in `.env`
3. Run migrations:
```bash
python manage.py migrate
```

## Development Workflow

### Code Style
- Follow PEP 8 guidelines
- Use British English throughout
- Include docstrings for all classes and functions
- Add type hints for function parameters

### Testing
- Write unit tests for all new features
- Run tests before committing:
```bash
python manage.py test
```

### Authentication Implementation
- JWT-based authentication using `django-rest-framework-simplejwt`
- Token expiry: 15 minutes for access tokens
- Refresh tokens valid for 7 days

### Subscription System
- Feature access controlled via permission classes
- Area limits enforced at the model level
- Subscription status checked on critical operations

## Field Management System

### Models Overview

#### Field Model
The `Field` model is the core entity for managing agricultural land units. It includes:
- Geospatial data using GeoDjango
- Soil characteristics
- Planting and harvest dates
- Comprehensive validation methods

#### Supporting Models
1. **FieldSubdivision**
   - Manages zones within fields
   - Supports different purposes (crop, buffer, experimental)
   - Automatic area calculation

2. **CropRotation**
   - Tracks historical and planned crop rotations
   - Prevents scheduling conflicts
   - Success rating system

3. **PestDisease**
   - Records pest and disease incidents
   - Severity tracking
   - Treatment documentation

4. **FertilizerApplication**
   - Records application details
   - Weather condition tracking
   - Application method management

5. **EquipmentUsage**
   - Tracks machinery usage
   - Fuel consumption monitoring
   - Area coverage tracking

### Validation System

#### Geometry Validation
- Topology validation for field boundaries
- Overlap detection with configurable thresholds (default 1%)
- Vertex count limits and automatic simplification
- Elevation and slope constraints
- Water source proximity validation

#### Performance Optimizations
- Cached geometric calculations
- Optimized spatial queries
- Automatic geometry simplification
- Bulk operation support
- Efficient coordinate precision handling

### Testing

#### Running Field Tests
```bash
# Run all field-related tests
python manage.py test apps.fields.tests

# Run specific test cases
python manage.py test apps.fields.tests.test_models.FieldGeometryValidationTests
```

#### Test Coverage Requirements
- Minimum 90% coverage for new features
- All validation scenarios must be tested
- Performance tests must pass under specified thresholds

### Database Optimization

#### Indexes
The following indexes are maintained for optimal performance:
- Combined index on (owner, created_at, is_active)
- Spatial index on geometry field
- Indexes on elevation_data and soil_type fields

#### Query Optimization
- Use `select_related` and `prefetch_related` for related data
- Implement batch processing for bulk operations
- Cache frequently accessed calculated metrics

### Best Practices
1. Always use the model's clean() method for validation
2. Ensure geometric operations use the correct SRID
3. Handle equipment usage tracking in real-time
4. Maintain historical records for analysis

## Field Analysis and Reporting System

### Analysis Features
The analysis system provides interactive, real-time data exploration capabilities:

1. Pest Analysis
   - Trend identification
   - Weather correlation
   - Treatment effectiveness
   - Risk prediction

2. Irrigation Analysis
   - Efficiency metrics
   - Weather impact assessment
   - Schedule optimization
   - Usage patterns

3. Equipment Analysis
   - Utilization metrics
   - Maintenance patterns
   - Efficiency tracking
   - Cost optimization

4. Crop Performance Analysis
   - Success patterns
   - Yield factors
   - Seasonal comparisons
   - Optimization opportunities

### Report Generation
The reporting system generates static, formatted documents for record-keeping:

1. Activity Log Reports
   - Daily operations
   - Personnel records
   - Equipment usage
   - Field activities

2. Compliance Reports
   - Regulatory requirements
   - Documentation status
   - Certification tracking
   - Audit records

3. Inventory Reports
   - Stock levels
   - Usage history
   - Asset status
   - Reorder points

4. Financial Reports
   - Income/expense tracking
   - Cost analysis
   - Budget monitoring
   - ROI calculations

### Implementation Guidelines

1. Analysis Features
   - Use real-time data processing
   - Implement caching for performance
   - Include interactive visualizations
   - Provide actionable recommendations

2. Report Generation
   - Use templates for consistency
   - Support multiple formats (PDF, XLSX, CSV)
   - Include proper headers and metadata
   - Implement proper access controls

3. Testing Requirements
   - Unit tests for calculations
   - Integration tests for API endpoints
   - Performance tests for analysis features
   - Format validation for reports

## Security Considerations
- CSRF protection enabled
- XSS prevention measures in place
- SQL injection protection via ORM
- Rate limiting on authentication endpoints
