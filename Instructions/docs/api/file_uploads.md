# File Upload Examples

## CSV Field Coordinates Upload

### Using curl
```bash
# Upload CSV file with field coordinates
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/upload_csv/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'file=@/path/to/field_coordinates.csv' \
  -H 'Content-Type: multipart/form-data'

# Expected CSV format:
# latitude,longitude,field_name
# 51.5074,-0.1278,North Field
# 51.5075,-0.1279,South Field
```

### Using form-data
```bash
# Upload with additional metadata
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/upload_csv/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'file=@/path/to/field_coordinates.csv' \
  -F 'field_group=winter_crops' \
  -F 'description=Winter wheat fields'
```

## Soil Analysis Reports

### Single File Upload
```bash
# Upload PDF soil analysis
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/soil_analysis/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'file=@/path/to/soil_report.pdf' \
  -F 'field_id=123' \
  -F 'analysis_date=2024-01-20'
```

### Multiple Files Upload
```bash
# Upload multiple soil analysis files
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/soil_analysis/bulk/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'files[]=@/path/to/report1.pdf' \
  -F 'files[]=@/path/to/report2.pdf' \
  -F 'field_ids[]=123' \
  -F 'field_ids[]=124'
```

## Field Images

### Image Upload with Metadata
```bash
# Upload field image with EXIF data
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/images/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'image=@/path/to/field_image.jpg' \
  -F 'field_id=123' \
  -F 'capture_date=2024-01-20T10:30:00Z' \
  -F 'metadata={
      "device": "DJI Phantom 4 Pro",
      "altitude": "100m",
      "camera_angle": "45deg"
    }'
```

### Batch Image Upload
```bash
# Upload multiple field images
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/images/bulk/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'images[]=@/path/to/image1.jpg' \
  -F 'images[]=@/path/to/image2.jpg' \
  -F 'field_id=123' \
  -F 'tags[]=drone_survey' \
  -F 'tags[]=irrigation_check'
```

## Different Content Types

### JSON with Base64 Encoded File
```bash
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/documents/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "field_id": "123",
    "document_name": "Soil Analysis 2024",
    "file_content": "base64_encoded_content_here",
    "file_type": "application/pdf"
  }'
```

### URL-Encoded Form Data
```bash
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/quick_note/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  --data-urlencode 'field_id=123' \
  --data-urlencode 'note=Irrigation system checked' \
  --data-urlencode 'attachment=@/path/to/photo.jpg'
```

## File Upload Limits and Validation

### Size Limits
- Individual file: 5MB
- Batch upload: 20MB total
- Supported formats: CSV, PDF, JPG, PNG

### Example with Progress Bar
```bash
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/upload_csv/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'file=@/path/to/large_field_data.csv' \
  --progress-bar
```

### Handling Upload Errors
```bash
# Example response for invalid file type
# {
#   "error": "Invalid file type",
#   "allowed_types": ["csv", "pdf", "jpg", "png"],
#   "status": 400
# }

# Example response for file too large
# {
#   "error": "File too large",
#   "max_size": "5MB",
#   "status": 400
# }
```

## Testing File Uploads

### Test Invalid File Type
```bash
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/upload_csv/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'file=@/path/to/invalid.txt'
```

### Test File Size Limit
```bash
curl -X POST \
  'https://api.cropcompass.com/api/v1/fields/upload_csv/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -F 'file=@/path/to/too_large.csv' \
  -w 'Response: %{http_code}\nTime: %{time_total}s\nSize: %{size_upload} bytes\n'
```