# Subscription Management API Documentation

## Available Plans

### List Available Subscription Plans
```http
GET /api/subscriptions/available_plans/
```

**Response (200)**
```json
[
    {
        "id": "uuid",
        "name": "string",
        "price": "decimal",
        "billing_period": "string",
        "features": [
            "string"
        ],
        "max_team_members": "integer",
        "max_area": "decimal"
    }
]
```

## Current Subscription

### Get Current Subscription
```http
GET /api/subscriptions/current/
```

**Response (200)**
```json
{
    "id": "uuid",
    "plan": {
        "id": "uuid",
        "name": "string",
        "features": [
            "string"
        ]
    },
    "status": "string",
    "current_period_end": "datetime",
    "cancel_at_period_end": "boolean"
}
```

### Upgrade Subscription
```http
POST /api/subscriptions/upgrade/
Content-Type: application/json

{
    "plan_id": "uuid",
    "payment_token": "string"
}
```

**Response (200)**
```json
{
    "id": "uuid",
    "plan": {
        "id": "uuid",
        "name": "string"
    },
    "status": "string",
    "current_period_end": "datetime"
}
```

### Cancel Subscription
```http
POST /api/subscriptions/cancel/
```

**Response (204)**
No content

## Role-Based Access Control

### Available Roles

| Role | Description | Permissions |
|------|-------------|------------|
| OWNER | Farm Owner | Full access to all features |
| MANAGER | Farm Manager | Can manage team members and farm operations |
| WORKER | Farm Worker | Can input data and view assigned areas |
| VIEWER | Read Only | Can only view data |

### Role Hierarchy

```
OWNER
  └── MANAGER
       └── WORKER
            └── VIEWER
```

### Feature Access by Subscription Tier

| Feature | FREE | BASIC | PRO | ENTERPRISE |
|---------|------|-------|-----|------------|
| View Fields | ✓ | ✓ | ✓ | ✓ |
| Create Fields | - | ✓ | ✓ | ✓ |
| Team Members | - | 2 | 5 | Unlimited |
| Weather Data | - | Basic | Advanced | Advanced |
| Analytics | - | Basic | Advanced | Custom |