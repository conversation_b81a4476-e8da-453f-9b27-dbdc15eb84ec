# Error Handling

## Common Error Responses

### Authentication Errors

**401 Unauthorized**
```json
{
    "detail": "Invalid token or token expired"
}
```

### Permission Errors

**403 Forbidden**
```json
{
    "detail": "You do not have permission to perform this action"
}
```

### Subscription Errors

**402 Payment Required**
```json
{
    "error": "Subscription required for this feature",
    "required_tier": "string"
}
```

### Resource Not Found

**404 Not Found**
```json
{
    "detail": "Resource not found"
}
```

### Validation Errors

**400 Bad Request**
```json
{
    "field_name": [
        "Error message"
    ]
}
```

## Rate Limiting

All API endpoints are rate-limited based on the subscription tier:

| Tier | Requests/minute |
|------|----------------|
| FREE | 30 |
| BASIC | 60 |
| PRO | 120 |
| ENTERPRISE | 300 |

**429 Too Many Requests**
```json
{
    "detail": "Request limit exceeded",
    "retry_after": "integer"
}
```