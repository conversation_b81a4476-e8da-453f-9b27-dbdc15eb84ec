# Field Operations API

## Equipment Scheduling

### Endpoint: `/api/fields/{field_id}/schedule-equipment/`

Schedule equipment for field operations with optimal timing based on weather, soil conditions, and equipment availability.

#### Request
```http
POST /api/fields/{field_id}/schedule-equipment/
{
    "start_date": "2024-02-15T08:00:00Z",
    "end_date": "2024-02-20T17:00:00Z",
    "equipment_type": "tractor",
    "operation_type": "plowing"
}
```

#### Response
```json
{
    "schedule": [
        {
            "window": {
                "start_time": "2024-02-15T08:00:00Z",
                "end_time": "2024-02-15T17:00:00Z",
                "workability_score": 0.85
            },
            "equipment": {
                "id": "tractor_01",
                "type": "tractor",
                "efficiency_rating": 0.9
            },
            "estimated_fuel": {
                "diesel": 120.5,
                "reserve_needed": 18.075,
                "total_cost_estimate": 175.25
            },
            "labor_requirements": {
                "operator_hours": 8.0,
                "support_hours": 4.0,
                "setup_hours": 2.0,
                "total_hours": 14.0
            }
        }
    ],
    "conflicts": [],
    "alternatives": [
        {
            "window": {
                "start_time": "2024-02-16T08:00:00Z",
                "end_time": "2024-02-16T17:00:00Z"
            },
            "alternative_equipment": [
                {
                    "id": "tractor_02",
                    "type": "tractor"
                }
            ],
            "cost_impact": -50.25,
            "efficiency_impact": -0.1
        }
    ]
}
```

### Configuration Options

The equipment scheduling system can be configured through Django settings:

```python
OPTIMIZATION_THRESHOLDS = {
    'weather': {
        'heavy_rain': 5.0,  # mm
        'light_rain': 2.0,  # mm
        'high_wind': 20.0,  # m/s
        'moderate_wind': 15.0,  # m/s
        'impact': {
            'heavy_rain': 0.2,
            'light_rain': 0.6,
            'high_wind': 0.4,
            'moderate_wind': 0.7
        }
    },
    'soil': {
        'very_wet': 0.8,  # saturation level
        'moist': 0.6,     # saturation level
        'impact': {
            'very_wet': 0.3,
            'moist': 0.7
        }
    },
    'workability': {
        'minimum_score': 0.7,
        'minimum_window_days': 2
    }
}
```

### Error Handling

The API returns appropriate HTTP status codes:

- 200: Successful scheduling
- 400: Invalid request parameters
- 404: Field not found
- 422: Unable to generate schedule due to constraints
- 500: Internal server error

Error responses include detailed messages:

```json
{
    "error": "equipment_scheduling_error",
    "message": "Failed to schedule equipment: No suitable windows found",
    "details": {
        "constraints": ["weather_unsuitable", "equipment_unavailable"],
        "suggested_actions": [
            "Try different date range",
            "Check equipment maintenance schedule"
        ]
    }
}
```