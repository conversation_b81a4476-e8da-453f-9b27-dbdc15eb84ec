# API Examples with curl Commands

## User Management

### Get Current User Profile
```bash
curl -X GET \
  'https://api.cropcompass.com/api/users/me/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Update User Profile
```bash
curl -X PATCH \
  'https://api.cropcompass.com/api/users/me/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "company_name": "Green Fields Ltd",
    "total_area": 150.5
}'
```

### List Team Members
```bash
curl -X GET \
  'https://api.cropcompass.com/api/users/team_members/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Add Team Member
```bash
curl -X POST \
  'https://api.cropcompass.com/api/users/team_members/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "role": "WORKE<PERSON>",
    "username": "fieldworker1"
}'
```

## Subscription Management

### List Available Plans
```bash
curl -X GET \
  'https://api.cropcompass.com/api/subscriptions/available_plans/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Get Current Subscription
```bash
curl -X GET \
  'https://api.cropcompass.com/api/subscriptions/current/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

### Upgrade Subscription
```bash
curl -X POST \
  'https://api.cropcompass.com/api/subscriptions/upgrade/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "plan_id": "plan_H2KS82Kn29sKl",
    "payment_token": "tok_visa_testcard"
}'
```

### Cancel Subscription
```bash
curl -X POST \
  'https://api.cropcompass.com/api/subscriptions/cancel/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
```

## Authentication

### Login
```bash
curl -X POST \
  'https://api.cropcompass.com/api/auth/login/' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
}'
```

### Refresh Token
```bash
curl -X POST \
  'https://api.cropcompass.com/api/auth/token/refresh/' \
  -H 'Content-Type: application/json' \
  -d '{
    "refresh": "YOUR_REFRESH_TOKEN"
}'
```

## Common Testing Scenarios

### Test Rate Limiting
```bash
# Run multiple requests in quick succession
for i in {1..40}; do
  curl -X GET \
    'https://api.cropcompass.com/api/users/me/' \
    -H 'Authorization: Bearer YOUR_ACCESS_TOKEN'
  sleep 0.1
done
```

### Test Role Permissions
```bash
# Attempt manager action as worker
curl -X POST \
  'https://api.cropcompass.com/api/users/team_members/' \
  -H 'Authorization: Bearer WORKER_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>",
    "role": "VIEWER"
}'
```

### Test Subscription Features
```bash
# Attempt to access PRO feature with FREE tier
curl -X GET \
  'https://api.cropcompass.com/api/analytics/advanced/' \
  -H 'Authorization: Bearer FREE_TIER_TOKEN'
```

## Environment Variables
```bash
# Set up environment variables for testing
export CROPCOMPASS_API_URL='https://api.cropcompass.com'
export ACCESS_TOKEN='your_access_token'

# Use in commands
curl -X GET \
  "${CROPCOMPASS_API_URL}/api/users/me/" \
  -H "Authorization: Bearer ${ACCESS_TOKEN}"
```

## Response Examples

### Successful Profile Update
```bash
curl -X PATCH \
  'https://api.cropcompass.com/api/users/me/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "company_name": "Updated Farm Name"
  }'

# Expected Response:
# {
#   "id": "user_123",
#   "username": "farmer_john",
#   "email": "<EMAIL>",
#   "company_name": "Updated Farm Name",
#   "subscription_tier": "PRO",
#   "role": "OWNER"
# }
```

### Error Response Example
```bash
curl -X POST \
  'https://api.cropcompass.com/api/subscriptions/upgrade/' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "plan_id": "invalid_plan"
  }'

# Expected Response:
# {
#   "error": "Invalid plan selected",
#   "status": 400,
#   "code": "INVALID_PLAN"
# }
```