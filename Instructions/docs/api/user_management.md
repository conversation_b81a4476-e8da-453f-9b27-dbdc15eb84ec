# User Management API Documentation

## Authentication

All endpoints require authentication using JWT tokens. Include the token in the Authorization header:
```http
Authorization: Bearer <access_token>
```

## Profile Management

### Get Current User Profile
```http
GET /api/users/me/
```

**Response (200)**
```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "subscription_tier": "string",
    "role": "string",
    "company_name": "string",
    "total_area": "decimal",
    "subscription_expiry": "datetime"
}
```

### Update User Profile
```http
PATCH /api/users/me/
Content-Type: application/json

{
    "company_name": "string",
    "total_area": "decimal"
}
```

**Response (200)**
```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "company_name": "string",
    "total_area": "decimal"
}
```

## Team Management

### List Team Members
```http
GET /api/users/team_members/
```

**Response (200)**
```json
[
    {
        "id": "uuid",
        "username": "string",
        "email": "string",
        "role": "string",
        "last_login": "datetime"
    }
]
```

### Add Team Member
```http
POST /api/users/team_members/
Content-Type: application/json

{
    "email": "string",
    "role": "string",
    "username": "string"
}
```

**Response (201)**
```json
{
    "id": "uuid",
    "username": "string",
    "email": "string",
    "role": "string"
}
```

## Field Operations API

### Optimize Field Operations
```http
POST /api/fields/{field_id}/optimize_operations/
Authorization: Bearer <token>
Content-Type: application/json

{
    "operation_type": "string",
    "start_date": "datetime",
    "end_date": "datetime"
}

Response 200:
{
    "optimal_dates": [
        {
            "date": "2024-02-15",
            "suitability_score": 0.85,
            "weather_conditions": "favorable"
        }
    ],
    "equipment_schedule": {
        "tractor_1": [
            {
                "start_time": "2024-02-15T08:00:00Z",
                "duration": "4h",
                "operation": "plowing"
            }
        ]
    },
    "resource_requirements": {
        "equipment": ["tractor", "plow"],
        "personnel": 2,
        "estimated_duration": "4h"
    },
    "constraints": [
        "weather_dependent",
        "equipment_availability"
    ],
    "recommendations": [
        "Start operation early morning for optimal soil conditions",
        "Ensure equipment maintenance before operation"
    ]
}
```

### Resource Allocation
```http
GET /api/fields/{field_id}/resource_allocation/?type=equipment
Authorization: Bearer <token>

Response 200:
{
    "equipment": {
        "available": [
            {
                "id": "tractor_01",
                "type": "tractor",
                "availability": "2024-02-15T08:00:00Z"
            }
        ],
        "recommended": [
            {
                "equipment_type": "tractor",
                "count": 1,
                "duration": "4h"
            }
        ]
    },
    "personnel": {
        "required": 2,
        "available": 3,
        "skills_required": ["tractor_operation", "field_work"]
    },
    "materials": {
        "type": "fertilizer",
        "quantity": "200kg",
        "estimated_cost": "150.00"
    },
    "schedule": {
        "start_date": "2024-02-15T08:00:00Z",
        "end_date": "2024-02-15T12:00:00Z",
        "dependencies": ["weather_suitable", "soil_moisture_optimal"]
    },
    "cost_analysis": {
        "equipment_cost": "100.00",
        "personnel_cost": "160.00",
        "materials_cost": "150.00",
        "total_cost": "410.00"
    }
}
```

### Yield Prediction
```http
POST /api/fields/{field_id}/predict_yield/
Authorization: Bearer <token>
Content-Type: application/json

{
    "crop_type": "string",
    "planting_date": "datetime"
}

Response 200:
{
    "predicted_yield": {
        "value": 4.5,
        "unit": "tonnes/hectare",
        "prediction_date": "2024-09-15"
    },
    "confidence_interval": {
        "lower": 4.2,
        "upper": 4.8,
        "confidence_level": 0.95
    },
    "influencing_factors": [
        {
            "factor": "soil_quality",
            "impact": "positive",
            "weight": 0.3
        },
        {
            "factor": "weather_forecast",
            "impact": "neutral",
            "weight": 0.2
        }
    ],
    "optimization_suggestions": [
        {
            "suggestion": "Adjust planting date",
            "potential_impact": "+0.3 tonnes/hectare",
            "confidence": 0.85
        }
    ]
}
```

### Error Responses

#### 400 Bad Request
```json
{
    "error": "Invalid operation type specified"
}
```

#### 403 Forbidden
```json
{
    "detail": "You do not have permission to perform this action"
}
```

#### 404 Not Found
```json
{
    "detail": "Field not found"
}
```

## Feature Access Requirements

The following subscription tiers are required for each endpoint:

| Endpoint | Required Tier |
|----------|---------------|
| Optimize Operations | PROFESSIONAL |
| Resource Allocation | PROFESSIONAL |
| Yield Prediction | ENTERPRISE |
