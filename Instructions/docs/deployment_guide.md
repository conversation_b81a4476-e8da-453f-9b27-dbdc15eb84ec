# Deployment Guide

## Prerequisites

### System Requirements
- Ubuntu 22.04 LTS
- Python 3.13+
- PostgreSQL 15+ with PostGIS
- Nginx
- Redis
- GDAL 3.10.2+

### Domain and SSL
- Valid domain name
- SSL certificate (Let's Encrypt recommended)

## Installation Steps

### 1. System Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install python3.13 python3.13-venv postgresql-15 postgresql-15-postgis nginx redis-server gdal-bin
```

### 2. PostgreSQL Setup
```bash
# Create database and enable PostGIS
sudo -u postgres psql
CREATE DATABASE cropcompass;
\c cropcompass
CREATE EXTENSION postgis;
```

### 3. Application Setup
```bash
# Clone repository
git clone https://github.com/your-repo/cropcompass.git
cd cropcompass

# Setup virtual environment
python3.13 -m venv venv
source venv/bin/activate
pip install -r requirements/production.txt

# Configure environment
cp .env.example .env
# Edit .env with production settings
```

### 4. Nginx Configuration
```nginx
server {
    listen 80;
    server_name cropcompass.example.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /static/ {
        alias /path/to/cropcompass/static/;
    }
}
```

### 5. Gunicorn Setup
```bash
# Install Gunicorn
pip install gunicorn

# Create systemd service
sudo nano /etc/systemd/system/cropcompass.service

[Unit]
Description=CropCompass Gunicorn Daemon
After=network.target

[Service]
User=cropcompass
Group=www-data
WorkingDirectory=/path/to/cropcompass
ExecStart=/path/to/cropcompass/venv/bin/gunicorn cropcompass.wsgi:application

[Install]
WantedBy=multi-user.target
```

## Monitoring and Maintenance

### Log Monitoring
```bash
# Application logs
tail -f /var/log/cropcompass/app.log

# Nginx logs
tail -f /var/log/nginx/access.log
```

### Backup Strategy
```bash
# Database backup
pg_dump -Fc cropcompass > backup.dump

# Restore from backup
pg_restore -d cropcompass backup.dump
```