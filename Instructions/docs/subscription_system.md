# Subscription System

## Overview
CropCompass implements a tiered subscription system with varying features and limits based on subscription level.

## Subscription Tiers

### Free Tier
- Basic field management
- Weather view access
- Basic calculations
- Limits:
  - 1 field maximum
  - 5 hectares total area
  - Basic features only

### Basic Tier
- All Free features
- Advanced field management
- Basic analytics
- Data export capabilities
- Limits:
  - 5 fields maximum
  - 50 hectares total area

### Professional Tier
- All Basic features
- Advanced analytics
- Yield prediction
- Irrigation optimization
- Limits:
  - 20 fields maximum
  - 200 hectares total area

### Enterprise Tier
- All Professional features
- API access
- Custom integrations
- No field or area limits
- Priority support

## Implementation Details

### Subscription Service
The `SubscriptionService` class provides core subscription functionality:

```python
from apps.subscriptions.services.subscription_service import SubscriptionService

# Check feature access
SubscriptionService.check_feature_access(user, 'advanced_analytics')

# Get subscription limits
limits = SubscriptionService.check_subscription_limits(user)

# Update subscription
SubscriptionService.update_user_subscription(user, plan, end_date)
```

### Permission Classes
Custom permission classes for feature access control:

```python
from apps.subscriptions.permissions import HasFeatureAccess, HasAreaLimit

@permission_classes([HasFeatureAccess('advanced_analytics')])
def my_view(request):
    pass
```

## API Endpoints

### Subscription Management
- `GET /api/subscriptions/current/`: Get current subscription details
- `POST /api/subscriptions/upgrade/`: Upgrade subscription
- `POST /api/subscriptions/cancel/`: Cancel subscription
- `GET /api/subscriptions/features/`: List available features

## Feature Access Control
Features are controlled through the `SUBSCRIPTION_FEATURES` setting:

```python
SUBSCRIPTION_FEATURES = {
    'FREE': {
        'basic_field_management',
        'weather_view',
        'basic_calculations'
    },
    'BASIC': {
        # Basic tier features
    },
    # Additional tiers...
}
```