# API Documentation

## Authentication

### Headers
All authenticated requests must include:
```
Authorization: Bearer <access_token>
```

### Authentication Endpoints

#### Login
```http
POST /api/auth/login/
Content-Type: application/json

{
    "username": "string",
    "password": "string"
}

Response 200:
{
    "access": "string",
    "refresh": "string"
}
```

#### Refresh Token
```http
POST /api/auth/token/refresh/
Content-Type: application/json

{
    "refresh": "string"
}

Response 200:
{
    "access": "string"
}
```

## User Management

### Get Current User Profile
```http
GET /api/users/profile/
Authorization: Bearer <token>

Response 200:
{
    "id": integer,
    "username": "string",
    "email": "string",
    "subscription_tier": "string",
    "company_name": "string",
    "total_area": number
}
```

### Update Profile
```http
PATCH /api/users/profile/
Authorization: Bearer <token>
Content-Type: application/json

{
    "company_name": "string",
    "email": "string"
}

Response 200:
{
    "id": integer,
    "username": "string",
    ...
}
```

## Subscription Management

### Get Current Subscription
```http
GET /api/subscriptions/current/
Authorization: Bearer <token>

Response 200:
{
    "plan": "string",
    "features": ["string"],
    "limits": {
        "max_fields": integer,
        "max_area": number
    },
    "expires_at": "datetime"
}
```

## Field Management

### Field Validation Rules

#### Geometry Constraints
- Maximum vertex count: 5000 (auto-simplified if exceeded)
- Minimum distance from water sources: 50m
- Maximum distance from water sources: 5000m
- Maximum slope: 15%
- Elevation range: -100m to 2500m above sea level

#### Subscription Limits
- Field area limited by subscription tier
- Maximum field count per subscription
- Additional features based on subscription level

### Performance Considerations

#### Bulk Operations
```http
POST /api/fields/bulk/
Content-Type: application/json
Authorization: Bearer <token>

{
    "fields": [
        {
            "name": "Field 1",
            "geometry": {...},
            "soil_type": "clay_loam"
        },
        ...
    ]
}
```

#### Optimized Queries
- Use `bounds` parameter for spatial queries:
```http
GET /api/fields/?bounds=<minLng>,<minLat>,<maxLng>,<maxLat>
```

- Include related data with `include` parameter:
```http
GET /api/fields/?include=soil_records,current_crop
```

### Field Operations

#### Create Field
```http
POST /api/fields/
Content-Type: application/json
Authorization: Bearer <token>

{
    "name": "North Field",
    "geometry": {
        "type": "Polygon",
        "coordinates": [[[30.0, 10.0], [40.0, 40.0], [20.0, 40.0], [30.0, 10.0]]]
    },
    "soil_type": "clay_loam",
    "soil_ph": 6.5,
    "soil_organic_matter": 3.5,
    "planting_date": "2024-03-15",
    "expected_harvest_date": "2024-09-15"
}
```

#### Create Field Subdivision
```http
POST /api/fields/{field_id}/subdivisions/
Content-Type: application/json
Authorization: Bearer <token>

{
    "name": "Zone A",
    "geometry": {
        "type": "Polygon",
        "coordinates": [...]
    },
    "purpose": "CROP",
    "notes": "High-yield wheat zone"
}
```

#### Record Crop Rotation
```http
POST /api/fields/{field_id}/rotations/
Content-Type: application/json
Authorization: Bearer <token>

{
    "crop_id": 123,
    "start_date": "2024-03-15",
    "end_date": "2024-09-15",
    "is_planned": false,
    "success_rating": 4,
    "notes": "Good yield despite dry conditions"
}
```

#### Record Pest/Disease Incident
```http
POST /api/fields/{field_id}/pest-incidents/
Content-Type: application/json
Authorization: Bearer <token>

{
    "name": "Wheat Rust",
    "type": "DISEASE",
    "severity": 3,
    "detection_date": "2024-04-01",
    "affected_area": 2.5,
    "treatment": "Applied fungicide treatment"
}
```

#### Record Fertilizer Application
```http
POST /api/fields/{field_id}/fertilizer-applications/
Content-Type: application/json
Authorization: Bearer <token>

{
    "fertilizer_type": "NPK 20-10-10",
    "application_date": "2024-04-15",
    "amount_per_hectare": 250.5,
    "method": "BROADCAST",
    "weather_conditions": {
        "temperature": 18.5,
        "humidity": 65,
        "wind_speed": 8.2
    }
}
```

#### Record Equipment Usage
```http
POST /api/fields/{field_id}/equipment-usage/
Content-Type: application/json
Authorization: Bearer <token>

{
    "equipment_id": 456,
    "start_time": "2024-04-15T08:00:00Z",
    "end_time": "2024-04-15T16:00:00Z",
    "operation_type": "PLOWING",
    "fuel_used": 45.5,
    "area_covered": 8.5
}
```

### Field Analytics

#### Get Equipment Usage Summary
```http
GET /api/fields/{field_id}/equipment-summary?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer <token>

Response 200:
{
    "total_fuel": 245.5,
    "total_hours": "125:30:00",
    "operations_breakdown": {
        "PLOWING": 3,
        "PLANTING": 1,
        "SPRAYING": 5
    }
}
```

### Field Analytics Dashboard

#### Get Field Analytics Dashboard
```http
GET /api/fields/{field_id}/analytics-dashboard/
Authorization: Bearer <token>

Response 200:
{
    "current_status": {
        "crop": "Wheat",
        "planting_date": "2024-03-15",
        "soil_health": {
            "ph": 6.5,
            "organic_matter": 3.2
        },
        "weather": {
            "temperature": 22.5,
            "humidity": 65,
            "wind_speed": 8.2
        }
    },
    "irrigation_analysis": {
        "current_method": "DRIP",
        "efficiency_score": 85.5,
        "total_water_usage": 1250.5,
        "average_rate": 2.5,
        "application_count": 45,
        "recommendations": [
            "Current irrigation system is optimal for slope",
            "Consider adjusting irrigation rate during peak summer"
        ]
    },
    "slope_analysis": {
        "slope_degree": 3.5,
        "risk_level": "MODERATE",
        "recommended_practices": [
            "Maintain vegetative cover",
            "Use erosion control measures"
        ]
    },
    "equipment_usage": {
        "total_fuel": 1250.5,
        "total_hours": "320:45:00"
    },
    "crop_performance": [
        {
            "crop__name": "Wheat",
            "avg_success": 4.2,
            "count": 3
        }
    ],
    "pest_disease_summary": [
        {
            "type": "DISEASE",
            "count": 2,
            "avg_severity": 3.5,
            "total_affected_area": 5.2
        }
    ],
    "field_metrics": {
        "total_area": 25.5,
        "average_elevation": 150.5,
        "average_slope": 2.3
    }
}
```

### Field Reports API

### Get Field Report
```http
GET /api/fields/{field_id}/reports/{report_type}
Authorization: Bearer <token>

Parameters:
- report_type: string (crop_rotation, pest_disease, equipment_usage, irrigation)
- start_date: string (YYYY-MM-DD)
- end_date: string (YYYY-MM-DD)

Response 200:
{
    "records": [...],
    "summary": {
        // Varies by report type
    },
    "graphs": [...],
    "recommendations": [...]
}
```

### Report Types

#### Pest/Disease Report
Provides comprehensive analysis of pest and disease incidents, including:
- Incident records with severity tracking
- Treatment effectiveness analysis
- Seasonal risk patterns
- Specific recommendations based on pest type and severity
- Success rate of different treatment methods

#### Equipment Usage Report
Monitors and analyzes equipment utilization:
- Usage patterns and efficiency metrics
- Fuel consumption analysis
- Maintenance scheduling recommendations
- Equipment rotation suggestions
- Cost optimization recommendations

#### Irrigation Report
Analyzes irrigation practices and efficiency:
- Water usage patterns
- Weather impact analysis
- Efficiency metrics
- Schedule optimization
- Cost-saving recommendations

### Recommendation System
The system provides data-driven recommendations based on:
- Historical patterns
- Current conditions
- Best practices
- Treatment effectiveness
- Weather conditions
- Equipment efficiency
- Water usage patterns

Recommendations are prioritized by:
1. Severity/urgency
2. Cost-effectiveness
3. Implementation feasibility
4. Environmental impact

### Example Response
```json
{
    "records": [
        {
            "type": "pest_disease",
            "detection_date": "2024-01-15",
            "severity": 3,
            "treatment": "Organic pesticide application",
            "resolution_date": "2024-01-28"
        }
    ],
    "summary": {
        "total_incidents": 5,
        "average_severity": 2.8,
        "average_resolution_time": "12.5 days",
        "most_common_type": "Aphids"
    },
    "recommendations": [
        "Apply insecticidal soap to affected areas",
        "Increase beneficial insect population",
        "Remove heavily infested plant parts"
    ]
}
```

### Pest and Disease Management

#### Supported Crops
- Arabica Coffee
- Other general crops

#### Pest Categories
1. Coffee-Specific Pests
   - Coffee Berry Borer
   - Coffee Leaf Rust
   - Coffee Leaf Miner

2. General Crop Pests
   - Aphids
   - Nematodes
   - Mealybugs
   - Scale Insects

3. Diseases
   - Anthracnose
   - Bacterial Blight
   - Root Rot

#### Recommendation System
The system provides targeted recommendations based on:
- Pest/disease type
- Severity level (1-5)
- Seasonal conditions
- Current weather patterns
- Historical treatment effectiveness

Recommendations include:
- Immediate control measures
- Preventive actions
- Long-term management strategies
- Resistance management
- Integration with IPM practices

#### Example Response for Coffee Berry Borer
```json
{
    "pest_analysis": {
        "type": "coffee_berry_borer",
        "severity": 4,
        "affected_area": "2.5 hectares",
        "detection_date": "2024-03-15"
    },
    "recommendations": [
        "Apply approved insecticides to affected areas",
        "Implement strict field sanitation practices",
        "Remove and destroy infested berries",
        "Increase monitoring frequency due to wet season",
        "Rotate chemical classes to prevent resistance development"
    ],
    "treatment_history": {
        "success_rate": 0.75,
        "most_effective_treatment": "Beauveria bassiana application",
        "average_resolution_time": "18 days"
    }
}
```

## Field Analysis and Reporting

### Analysis Endpoints

#### Get Pest Analysis
```http
GET /api/fields/{field_id}/analysis/pests/
Content-Type: application/json
Authorization: Bearer <token>

Query Parameters:
{
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "include_weather": boolean,    // Optional: include weather correlation
    "include_treatments": boolean  // Optional: include treatment effectiveness
}

Response 200:
{
    "trends": {
        "seasonal_patterns": [...],
        "risk_factors": [...],
        "correlations": [...]
    },
    "recommendations": [...],
    "interactive_data": {
        "timeline": [...],
        "severity_distribution": [...],
        "treatment_outcomes": [...]
    }
}
```

#### Get Irrigation Analysis
```http
GET /api/fields/{field_id}/analysis/irrigation/
Content-Type: application/json
Authorization: Bearer <token>

Query Parameters:
{
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "include_weather": boolean     // Optional: include weather impact
}

Response 200:
{
    "efficiency_metrics": {...},
    "optimization_opportunities": [...],
    "recommendations": [...],
    "interactive_data": {
        "daily_usage": [...],
        "efficiency_trends": [...],
        "weather_correlation": [...]
    }
}
```

#### Get Equipment Utilization Analysis
```http
GET /api/fields/{field_id}/analysis/equipment/
Content-Type: application/json
Authorization: Bearer <token>

Query Parameters:
{
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "equipment_type": "string"     // Optional: filter by equipment type
}

Response 200:
{
    "utilization_metrics": {...},
    "efficiency_analysis": {...},
    "maintenance_patterns": [...],
    "recommendations": [...],
    "interactive_data": {
        "usage_patterns": [...],
        "efficiency_trends": [...],
        "maintenance_timeline": [...]
    }
}
```

#### Get Crop Performance Analysis
```http
GET /api/fields/{field_id}/analysis/crops/
Content-Type: application/json
Authorization: Bearer <token>

Query Parameters:
{
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "crop_type": "string"         // Optional: filter by crop type
}

Response 200:
{
    "success_patterns": {...},
    "yield_factors": [...],
    "optimization_opportunities": [...],
    "recommendations": [...],
    "interactive_data": {
        "success_timeline": [...],
        "yield_patterns": [...],
        "seasonal_comparison": [...]
    }
}
```

### Report Endpoints

#### Generate Field Report
```http
POST /api/fields/{field_id}/reports/
Content-Type: application/json
Authorization: Bearer <token>

Request Body:
{
    "report_type": "activity_log|compliance|inventory|financial",
    "start_date": "2024-01-01",
    "end_date": "2024-03-23",
    "format": "xlsx|csv|pdf"
}

Response 200:
Binary file stream with appropriate Content-Type header
```

#### Available Report Types

1. Activity Log Report
   - Daily field activities
   - Personnel assignments
   - Equipment usage records
   - Static summary of operations

2. Compliance Report
   - Regulatory requirements status
   - Documentation records
   - Certification tracking
   - Audit trail

3. Inventory Report
   - Current stock levels
   - Usage history
   - Reorder points
   - Asset status

4. Financial Report
   - Income and expenses
   - Cost per activity
   - Budget tracking
   - ROI calculations

## Error Responses

### 401 Unauthorized
```json
{
    "detail": "Invalid token."
}
```

### 403 Forbidden
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### 429 Too Many Requests
```json
{
    "detail": "Request limit exceeded."
}
```
