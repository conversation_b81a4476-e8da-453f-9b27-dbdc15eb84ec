# Calculation Engine Documentation

## Overview

The Calculation Engine is a core component of the CropCompass application that provides various agricultural calculations and optimizations. It is designed to be modular, extensible, and subscription-tier aware, allowing for easy addition of new calculators and integration with the rest of the application.

## Architecture

The Calculation Engine follows a modular architecture with the following components:

### Core Components

1. **Base Engine Class**: Provides a common interface and functionality for all calculation engines.
2. **Engine Registry**: Manages the registration and discovery of calculation engines.
3. **Calculation Service**: Provides a high-level interface for executing calculations and handling subscription tier validation.
4. **API Endpoints**: Exposes the calculation engines through RESTful API endpoints.

### Calculators

The Calculation Engine includes the following calculators:

1. **Water Requirements Calculator**: Calculates water requirements for crops based on field and climate data.
2. **Plant Spacing Calculator**: Calculates optimal plant spacing based on crop type and field characteristics.
3. **Field Orientation Optimizer**: Optimizes field orientation based on sunlight, wind, and topography.
4. **Cost Estimation Engine**: Estimates costs for agricultural operations based on field and crop data.

### Utilities

The Calculation Engine includes the following utilities:

1. **Validators**: Utilities for validating calculation inputs.
2. **Converters**: Utilities for converting between different units and formats.
3. **Formatters**: Utilities for formatting calculation results.

## Subscription Tiers

The Calculation Engine supports the following subscription tiers:

1. **Basic**: Includes the Water Requirements Calculator and Plant Spacing Calculator.
2. **Standard**: Includes all Basic tier calculators plus the Field Orientation Optimizer.
3. **Premium**: Includes all Standard tier calculators plus the Cost Estimation Engine.

## API Endpoints

The Calculation Engine exposes the following API endpoints:

1. **GET /api/calculations/**: Lists all available calculation engines for the current user.
2. **POST /api/calculations/execute/**: Executes a calculation using the specified engine.
3. **POST /api/calculations/water-requirements/**: Calculates water requirements.
4. **POST /api/calculations/plant-spacing/**: Calculates plant spacing.
5. **POST /api/calculations/field-orientation/**: Optimizes field orientation.
6. **POST /api/calculations/cost-estimation/**: Estimates costs.

## Usage Examples

### Water Requirements Calculator

```json
// Request
POST /api/calculations/water-requirements/
{
  "crop_type": "corn",
  "field_area": 10.5,
  "soil_type": "loam",
  "climate_zone": "semi_arid"
}

// Response
{
  "status": "success",
  "errors": null,
  "results": {
    "daily_water_requirement": 60.0,
    "weekly_water_requirement": 420.0,
    "monthly_water_requirement": 1800.0,
    "unit": "cubic_meters",
    "crop_coefficient": 1.2,
    "reference_evapotranspiration": 5.0,
    "soil_adjustment_factor": 1.0,
    "recommended_irrigation": {
      "method": "sprinkler",
      "reason": "Sprinkler irrigation is suitable for field crops like corn and wheat.",
      "schedule": {
        "frequency": "Every 2 day(s)",
        "water_per_irrigation": 120.0,
        "recommended_time": "early_morning",
        "notes": [
          "Adjust irrigation based on rainfall and temperature changes.",
          "Critical irrigation periods are during tasseling and grain filling.",
          "Monitor soil moisture closely as sandy soils drain quickly."
        ]
      }
    }
  },
  "metadata": {
    "engine_name": "water_requirements",
    "engine_version": "1.0.0",
    "subscription_tier": "basic"
  }
}
```

### Plant Spacing Calculator

```json
// Request
POST /api/calculations/plant-spacing/
{
  "crop_type": "tomato",
  "field_area": 5.0,
  "planting_method": "row"
}

// Response
{
  "status": "success",
  "errors": null,
  "results": {
    "row_spacing": 1.0,
    "plant_spacing": 0.4,
    "unit": "meters",
    "plants_per_square_meter": 2.5,
    "total_plants": 12500,
    "num_rows": 22,
    "plants_per_row": 55,
    "seed_quantity": {
      "seeds_needed": 16667,
      "weight_grams": 55.56,
      "weight_kg": 0.056,
      "germination_rate": 75.0
    },
    "recommendations": [
      "Plant tomato with 1.00m between rows and 0.40m between plants.",
      "Ensure rows are straight and properly spaced for efficient machinery operation.",
      "Consider using stakes or cages for support as plants grow."
    ]
  },
  "metadata": {
    "engine_name": "plant_spacing",
    "engine_version": "1.0.0",
    "subscription_tier": "basic"
  }
}
```

### Field Orientation Optimizer

```json
// Request
POST /api/calculations/field-orientation/
{
  "latitude": 42.5,
  "longitude": -89.0,
  "crop_type": "corn",
  "slope": 3.5,
  "prevailing_wind": 270
}

// Response
{
  "status": "success",
  "errors": null,
  "results": {
    "optimal_orientation": 81.0,
    "unit": "degrees",
    "reference": "degrees clockwise from north",
    "factors": {
      "sun_orientation": 0.0,
      "wind_orientation": 270.0,
      "slope_adjustment": 0.0,
      "crop_adjustment": 0
    },
    "row_direction": "East-Northeast",
    "benefits": [
      "Optimized sunlight exposure for photosynthesis",
      "Reduced risk of soil erosion",
      "Improved pollination through better pollen distribution",
      "Good morning sun exposure"
    ],
    "planting_pattern": {
      "pattern": "standard_rows",
      "description": "Standard row planting with uniform spacing.",
      "considerations": [
        "Ensure proper drainage to prevent waterlogging."
      ]
    }
  },
  "metadata": {
    "engine_name": "field_orientation",
    "engine_version": "1.0.0",
    "subscription_tier": "standard"
  }
}
```

### Cost Estimation Engine

```json
// Request
POST /api/calculations/cost-estimation/
{
  "crop_type": "corn",
  "field_area": 20.0,
  "region": "midwest_us",
  "soil_quality": "good"
}

// Response
{
  "status": "success",
  "errors": null,
  "results": {
    "total_cost": 38000.0,
    "cost_per_hectare": 1900.0,
    "currency": "USD",
    "cost_breakdown": {
      "seed": 5000.0,
      "fertilizer": 7000.0,
      "pesticides": 4000.0,
      "irrigation": 3000.0,
      "labor": 6000.0,
      "machinery": 8000.0,
      "harvest": 5000.0
    },
    "expected_yield": {
      "amount": 12.35,
      "unit": "tonnes",
      "total": 247.0
    },
    "financial_projection": {
      "market_price": 175.0,
      "expected_revenue": 43225.0,
      "expected_profit": 5225.0,
      "roi_percent": 13.8
    },
    "recommendations": [
      {
        "category": "fertilizer",
        "recommendation": "Implement precision agriculture techniques for targeted fertilizer application.",
        "potential_savings": "10-20%",
        "implementation_difficulty": "medium"
      },
      {
        "category": "machinery",
        "recommendation": "Share equipment with neighboring farms or use custom hiring services.",
        "potential_savings": "10-20%",
        "implementation_difficulty": "medium"
      },
      {
        "category": "labor",
        "recommendation": "Consider mechanization for labor-intensive tasks.",
        "potential_savings": "15-25%",
        "implementation_difficulty": "high"
      },
      {
        "category": "general",
        "recommendation": "Consider no-till farming to reduce machinery and labor costs.",
        "potential_savings": "10-15%",
        "implementation_difficulty": "medium"
      },
      {
        "category": "general",
        "recommendation": "Join local agricultural cooperatives for bulk purchasing and shared resources.",
        "potential_savings": "5-10%",
        "implementation_difficulty": "low"
      }
    ]
  },
  "metadata": {
    "engine_name": "cost_estimation",
    "engine_version": "1.0.0",
    "subscription_tier": "premium"
  }
}
```

## Extending the Calculation Engine

To add a new calculator to the Calculation Engine, follow these steps:

1. Create a new calculator class that inherits from `CalculationEngine`.
2. Implement the `validate_inputs` and `calculate` methods.
3. Set the `name`, `description`, `version`, and `subscription_tier` class attributes.
4. Register the calculator with the `EngineRegistry` using the `@EngineRegistry.register` decorator.
5. Add a convenience endpoint to the `CalculationViewSet` if desired.

Example:

```python
from backend.apps.calculations.engine.base import CalculationEngine
from backend.apps.calculations.engine.registry import EngineRegistry

@EngineRegistry.register
class MyNewCalculator(CalculationEngine):
    """My new calculator."""
    
    name = "my_new_calculator"
    description = "Description of my new calculator"
    version = "1.0.0"
    subscription_tier = "standard"
    
    def validate_inputs(self, inputs):
        # Implement validation logic
        pass
    
    def calculate(self, inputs):
        # Implement calculation logic
        pass
```

## Testing

The Calculation Engine includes comprehensive tests for each calculator. To run the tests, use the following command:

```bash
python manage.py test backend.apps.calculations
```

## Conclusion

The Calculation Engine provides a powerful and extensible framework for agricultural calculations and optimizations. It is designed to be modular, subscription-tier aware, and easy to integrate with the rest of the CropCompass application.
