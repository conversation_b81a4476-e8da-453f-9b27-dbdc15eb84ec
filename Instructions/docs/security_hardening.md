# Security Hardening Guide

## Django Security Settings

### Critical Settings
```python
# settings/production.py
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# Session security
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_TRUSTED_ORIGINS = ['https://cropcompass.example.com']

# Content security
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'DENY'
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# CORS settings
CORS_ALLOWED_ORIGINS = [
    'https://trusted-origin.com',
]
CORS_EXPOSE_HEADERS = ['Content-Type', 'X-Request-ID']
```

## API Security

### Rate Limiting
```python
# settings/production.py
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/day',
        'user': '1000/day',
        'subscription_tier_premium': '10000/day'
    }
}
```

### JWT Configuration
```python
# settings/production.py
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=1),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
}
```

## Database Security

### Connection Settings
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'OPTIONS': {
            'sslmode': 'verify-full',
            'sslcert': '/path/to/client-cert.pem',
            'sslkey': '/path/to/client-key.pem',
            'sslrootcert': '/path/to/server-ca.pem',
        },
    }
}
```

### PostgreSQL Hardening
```sql
-- Restrict network access
ALTER SYSTEM SET listen_addresses = 'localhost';

-- Enable SSL
ALTER SYSTEM SET ssl = on;
ALTER SYSTEM SET ssl_cert_file = 'server.crt';
ALTER SYSTEM SET ssl_key_file = 'server.key';

-- Set connection limits
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET superuser_reserved_connections = 3;
```

## Server Hardening

### Nginx Security Headers
```nginx
server {
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data: https:; style-src 'self' 'unsafe-inline';" always;
    
    # Disable server tokens
    server_tokens off;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
}
```

## File Upload Security

### Configuration
```python
# settings/production.py
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
FILE_UPLOAD_PERMISSIONS = 0o644
ALLOWED_UPLOAD_EXTENSIONS = ['.jpg', '.png', '.pdf', '.xlsx']

# Custom validation
def validate_file_upload(file):
    if file.size > settings.FILE_UPLOAD_MAX_MEMORY_SIZE:
        raise ValidationError("File too large")
    if not os.path.splitext(file.name)[1].lower() in settings.ALLOWED_UPLOAD_EXTENSIONS:
        raise ValidationError("Invalid file type")
```

## Monitoring and Alerts

### Security Monitoring
```python
# monitoring/security.py
SECURITY_ALERTS = {
    'login_attempts': {
        'threshold': 5,
        'period': 300,  # 5 minutes
        'action': 'block_ip'
    },
    'api_errors': {
        'threshold': 100,
        'period': 3600,  # 1 hour
        'action': 'notify_admin'
    }
}

# Example monitoring implementation
def monitor_login_attempts(user_ip):
    attempts = cache.get(f'login_attempts:{user_ip}', 0)
    if attempts >= SECURITY_ALERTS['login_attempts']['threshold']:
        block_ip(user_ip)
    cache.set(f'login_attempts:{user_ip}', attempts + 1, 
             SECURITY_ALERTS['login_attempts']['period'])
```

## Regular Security Tasks

### Checklist
1. Rotate encryption keys monthly
2. Update SSL certificates before expiration
3. Review access logs weekly
4. Audit user permissions quarterly
5. Update dependencies and check for security patches weekly

### Automation Script
```python
# security/maintenance.py
def security_maintenance():
    # Check SSL certificate expiration
    ssl_expiry = check_ssl_expiry()
    if ssl_expiry < timedelta(days=30):
        notify_admin("SSL certificate expiring soon")
    
    # Audit user permissions
    inactive_users = User.objects.filter(
        last_login__lte=timezone.now() - timedelta(days=90)
    )
    for user in inactive_users:
        disable_user_access(user)
    
    # Check dependency vulnerabilities
    run_safety_check()
```