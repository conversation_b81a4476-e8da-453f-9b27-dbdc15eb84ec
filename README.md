# CropCompass GPS

## Overview

CropCompass GPS is an iOS application designed for agricultural use, enabling users to accurately map field boundaries, record specific points of interest (spots), and track routes within fields using the device's GPS capabilities. It provides tools for managing field data, customising map appearance and units, configuring GPS settings, and exporting collected data in common formats.

## Features

*   **Field Management:** Create, name, and manage multiple fields.
*   **GPS Point Recording:**
    *   Record individual spot points with optional notes.
    *   Continuously record GPS tracks (routes) within a selected field.
*   **Map View:**
    *   Visualise fields, recorded points, and current location on a map.
    *   Selectable map styles (Standard, Hybrid, Satellite).
    *   Real-time GPS accuracy indicator.
    *   Compass display showing current heading.
    *   Optional custom direction indicator on the compass.
*   **Settings:**
    *   Choose between Metric and Imperial measurement systems.
    *   Configure GPS recording interval (distance filter).
    *   Select activity type (Walking, Cycling, Vehicle, Tractor) to optimise GPS filtering.
    *   Manage location permission settings.
*   **Data Export:** Export field data (boundaries, points, tracks) in GPX, KML, and CSV formats.
*   **Data Persistence:** Field and point data are saved locally on the device.

## Requirements

*   iOS 16.0 or later (Based on typical modern SwiftUI features, adjust if needed)
*   Xcode 15.0 or later
*   Device with GPS capabilities

## Setup and Installation

1.  **Clone the Repository:**
    ```bash
    git clone <repository_url>
    cd CropCompass_GPS
    ```
2.  **Open in Xcode:** Open the `CropCompass_GPS.xcodeproj` file in Xcode.
3.  **Configure Signing:** Select the project in the Project Navigator, go to the "Signing & Capabilities" tab, and select your development team.
4.  **Build and Run:** Select a target device or simulator and press the Run button (Cmd+R).

## Usage Guide

1.  **Home/Field Management:** The initial view likely lists existing fields. You can add new fields or select an existing one.
2.  **Map View:** After selecting a field, the map view shows the field boundary (if recorded), points, and your current location. Controls may be available to centre the map, change styles, or start recording.
3.  **Recording View:** When recording is active (either spot points or continuous tracking), this view might provide specific controls like "Mark Spot", "Stop Recording", and display real-time data like current coordinates, accuracy, and distance traveled. The compass is prominently displayed here.
4.  **Settings View:** Access settings via a dedicated tab or button. Here you can configure:
    *   **Map Style:** Standard, Hybrid, Imagery.
    *   **Units:** Metric (meters, km/h) or Imperial (feet, mph).
    *   **GPS Settings:** Adjust the recording interval (how often points are saved based on distance moved) and the activity type to help the system optimise location updates.
    *   **Location Permissions:** View current status and request necessary permissions.

## Configuration Details

The application requires specific keys in its `Info.plist` file to function correctly:

*   `NSLocationWhenInUseUsageDescription`: Explains why the app needs location access while being used.
*   `NSLocationAlwaysAndWhenInUseUsageDescription`: Explains why the app needs location access even in the background (for continuous tracking).
*   `UIBackgroundModes` (with `location` value): Declares the app's capability to receive location updates in the background.
*   `NSLocationTemporaryUsageDescriptionDictionary`: Explains why temporary full accuracy is needed when requested.

## Data Management

*   Field data, including names, boundaries, and recorded points/tracks, is stored locally using `UserDefaults`.
*   Data can be exported from the Export view in the following formats:
    *   **GPX:** Standard format for GPS data exchange.
    *   **KML:** Keyhole Markup Language, commonly used with Google Earth.
    *   **CSV:** Comma-Separated Values, suitable for spreadsheets.

## Contributing

(Add contribution guidelines if applicable)

## License

(Add license information if applicable) 