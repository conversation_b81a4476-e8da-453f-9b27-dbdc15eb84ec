//
//  CropCompass_GPSUITests.swift
//  CropCompass_GPSUITests
//
//  Created by <PERSON><PERSON> on 28/3/2025.
//

import XCTest
import Foundation

extension XCUIElement {
    func clearText() {
        guard let stringValue = self.value as? String else {
            NSLog("No text to clear in: \(self.identifier)")
            return
        }
        
        // iOS 13+ way to clear text
        self.tap()
        let deleteString = String(repeating: XCUIKeyboardKey.delete.rawValue, count: stringValue.count)
        self.typeText(deleteString)
        
        // Verify text was cleared
        if let remainingText = self.value as? String, !remainingText.isEmpty {
            NSLog("Warning: Text field not fully cleared. Remaining text: \(remainingText)")
        }
    }
}

final class CropCompass_GPSUITests: XCTestCase {
    
    var app: XCUIApplication!
    var testSpecificUserDefaultsDomain: String? // Store the unique domain name
    let defaultTimeout: TimeInterval = 5.0 // Adjusted standard default wait time

    override func setUpWithError() throws {
        NSLog("\n=== Test Setup Starting ===")

        // Stop immediately when a failure occurs
        continueAfterFailure = false

        // Initialize the app
        app = XCUIApplication()
        
        // Generate unique suite name for THIS test run
        let suiteName = "UITesting-\(UUID().uuidString)"
        testSpecificUserDefaultsDomain = suiteName
        NSLog("Setting UI test UserDefaults domain: \(suiteName)")
        
        // Add launch arguments for testing
        app.launchArguments = [
            "-ui-testing",
            "-use-test-location",  // Enable location simulation
            "-AppleLanguages", "(en-GB)",  // Force British English
            "-AppleLocale", "en_GB",
            "-UIPreferredContentSizeCategoryName", "UICTContentSizeCategoryL",  // Force standard text size
            "-UserDefaultsDomain", suiteName,  // Use test-specific UserDefaults
            "-UITEST_DISABLE_ANIMATIONS", "YES",  // Disable animations for more reliable tests
            "-UITEST_DISABLE_API_CALLS", "YES"   // Disable real API calls during testing
        ]
        
        // Set up test location coordinates for London
        app.launchEnvironment = [
            "TEST_LATITUDE": "51.5074",    // London coordinates for testing
            "TEST_LONGITUDE": "-0.1278",
            "TEST_HORIZONTAL_ACCURACY": "5.0",  // 5m accuracy for testing
            "TEST_COURSE": "0",                 // Heading north
            "TEST_SPEED": "1",                  // Simulate 1 m/s movement to trigger distance filter
            "UITEST_DISABLE_ANIMATIONS": "YES",  // Disable animations for more reliable tests
            "UITEST_MOCK_DATA": "YES"           // Use mock data for testing
        ]
        
        NSLog("Launch Arguments: \(app.launchArguments)")
        NSLog("Launch Environment: \(app.launchEnvironment)")
        
        // Launch the app
        NSLog("Launching application...")
        app.launch()
        
        // Wait for app to be fully ready
        guard app.wait(for: .runningForeground, timeout: 20) else {
            XCTFail("App did not enter running state within 20 seconds")
            return
        }
        NSLog("App launched successfully")
        
        // Wait for initial navigation bar to ensure base UI is loaded
        let firstNavBar = app.navigationBars.firstMatch
        guard firstNavBar.waitForExistence(timeout: 20) else {
            NSLog("Initial view state verification failed: No Navigation Bar found.")
            NSLog("Current view hierarchy:\n%@", app.debugDescription)
            XCTFail("No Navigation Bar found after 20 seconds")
            return
        }
        NSLog("Initial Navigation Bar found.") // Removed specific title check
        
        // Verify GPS simulation
        NSLog("Verifying GPS simulation...")
        try verifyGPSSimulation()
        
        NSLog("=== Test Setup Completed ===\n")
    }

    override func tearDownWithError() throws {
        NSLog("\n=== Test Teardown Starting ===")
        
        // Clean up the specific UserDefaults domain used for this test run
        if let domain = testSpecificUserDefaultsDomain {
            NSLog("Removing UI test UserDefaults domain: \(domain)")
            UserDefaults.standard.removePersistentDomain(forName: domain)
            UserDefaults.standard.synchronize()
            testSpecificUserDefaultsDomain = nil
        }
        
        // Take a screenshot if the test failed
        if XCTContext.runActivity(named: "Capture failure state", block: { activity in
            let screenshot = XCUIScreen.main.screenshot()
            let attachment = XCTAttachment(screenshot: screenshot)
            attachment.lifetime = .deleteOnSuccess
            activity.add(attachment)
            return true
        }) {
            NSLog("Successfully captured failure screenshot")
        } else {
            NSLog("Failed to capture failure screenshot")
        }
        
        // Terminate the app
        if app != nil {
            NSLog("Terminating application...")
            app.terminate()
            app = nil
        }
        
        try super.tearDownWithError()
        NSLog("=== Test Teardown Completed ===\n")
    }

    @MainActor
    func testExample() throws {
        // UI tests must launch the application that they test.
        let app = XCUIApplication()
        app.launch()

        // Use XCTAssert and related functions to verify your tests produce the correct results.
    }

    @MainActor
    func testLaunchPerformance() throws {
        if #available(macOS 10.15, iOS 13.0, tvOS 13.0, watchOS 7.0, *) {
            // This measures how long it takes to launch your application.
            measure(metrics: [XCTApplicationLaunchMetric()]) {
                XCUIApplication().launch()
            }
        }
    }

    // MARK: - Helper Methods
    
    private func verifyGPSSimulation() throws {
        NSLog("\n=== Starting GPS Simulation Verification ===")
        let gpsIndicator = app.staticTexts["indicator.gps.status"]

        // 1. Wait for the indicator element itself to exist
        guard waitForElement(gpsIndicator, timeout: 15) else {
            XCTFail("GPS Signal Indicator (indicator.gps.status) did not appear within 15 seconds.")
            return
        }
        NSLog("DEBUG: GPS Indicator element exists.")

        // 2. Wait for the indicator's label to show a valid signal state
        // We expect it to eventually NOT contain initial/poor states like "No Signal" or "Acquiring".
        // Adjust the predicate based on the actual text seen during initialisation.
        let predicate = NSPredicate { (evaluatedObject, _) -> Bool in
            guard let element = evaluatedObject as? XCUIElement else { return false }
            let label = element.label
            NSLog("DEBUG: Checking GPS Status Label: '\(label)'") // Log current label value
            // Check if the label indicates a valid, acquired signal
            return !label.contains("No Signal") && !label.contains("Acquiring") && !label.isEmpty && !label.contains("Status: None") // Add other potential initial/error states
        }

        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: gpsIndicator)

        NSLog("DEBUG: Waiting up to 15s for GPS indicator label to show a valid signal...")
        let result = XCTWaiter.wait(for: [expectation], timeout: 15)

        if result == .completed {
            NSLog("DEBUG: GPS Ready state achieved (Label: '\(gpsIndicator.label)')")
        } else {
            let finalLabel = gpsIndicator.label // Get the label value at timeout
            NSLog("DEBUG: GPS Indicator final label value after timeout: '\(finalLabel)'")
            logAllElements(in: app) // Log hierarchy on failure
            XCTFail("Timed out waiting for GPS signal indicator to show a valid status. Final value: '\(finalLabel)'. Timeout: 15s")
        }

        NSLog("=== GPS Simulation Verification Completed ===")
    }
    
    private func waitForElement(_ element: XCUIElement, timeout: TimeInterval? = nil) -> Bool {
        let waitTime = timeout ?? defaultTimeout // Use provided timeout or default
        let startTime = Date()
        while !element.exists {
            if Date().timeIntervalSince(startTime) > waitTime {
                NSLog("Element \(element.identifier) (\(element.elementType)) not found within \(waitTime) seconds")
                return false
            }
            sleep(1)
        }
        
        NSLog("Element \(element.identifier) (\(element.elementType)) found!")
        // Optionally print hierarchy, keeping it concise
        // printElementHierarchy(element, level: 0) \n        return true
        return true
    }
    
    func waitForElementToBeHittable(_ element: XCUIElement, timeout: TimeInterval? = nil) -> Bool {
        let waitTime = timeout ?? defaultTimeout // Use provided timeout or default 5s
        NSLog("\n=== Waiting for Element to be Hittable (max \(waitTime)s) ===")
        NSLog("Element identifier: \(element.identifier)")
        NSLog("Element type: \(element.elementType)")
        
        let startTime = Date()
        
        // First check if element exists using the standard wait (or provided timeout)
        guard waitForElement(element, timeout: waitTime) else {
            NSLog("Element does not exist, cannot be hittable")
            return false
        }
        
        // Then wait for it to be hittable using the *full* waitTime
        let predicate = NSPredicate(format: "isHittable == true")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: element)
        
        // Calculate remaining time for the hittable check, but ensure it uses the full original waitTime
        // If waitForElement took time, we still give the full waitTime for hittability.
        let result = XCTWaiter().wait(for: [expectation], timeout: waitTime)
        
        let elapsed = Date().timeIntervalSince(startTime)
        NSLog("Total wait duration: \(String(format: "%.2f", elapsed))s")
        
        if result == .completed {
            NSLog("Element is hittable.")
        } else {
            NSLog("Element not hittable within \(waitTime)s.")
            // Log details only on failure to reduce noise
            NSLog("- Frame: \(element.frame)")
            NSLog("- Label: \(element.label)")
            NSLog("- Value: \(String(describing: element.value))")
            NSLog("- Enabled: \(element.isEnabled)")
        }
        
        return result == .completed
    }
    
    private func waitForElementToDisappear(_ element: XCUIElement, timeout: TimeInterval? = nil) -> Bool {
        let waitTime = timeout ?? defaultTimeout // Use provided timeout or default
        NSLog("\n=== Waiting for Element to Disappear (max \(waitTime)s) ===")
        NSLog("Element identifier: \(element.identifier)")
        NSLog("Element type: \(element.elementType)")
        
        let startTime = Date()
        let predicate = NSPredicate(format: "exists == false")
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: element)
        let result = XCTWaiter().wait(for: [expectation], timeout: waitTime)
        
        let elapsed = Date().timeIntervalSince(startTime)
        NSLog("Wait duration: \(String(format: "%.2f", elapsed))s")
        
        if result == .completed {
            NSLog("Element successfully disappeared")
            return true
        } else {
            NSLog("Element did not disappear within timeout.")
            // Log details only on failure
            NSLog("- Still exists: \(element.exists)")
            NSLog("- Still hittable: \(element.isHittable)")
            NSLog("- Current frame: \(element.frame)")
            return false
        }
    }
    
    private func waitForSheetAndDismiss(_ sheet: XCUIElement, dismissButton: XCUIElement, timeout: TimeInterval = 10) -> Bool {
        NSLog("\n=== Handling Sheet Dismissal ===")
        NSLog("Sheet identifier: \(sheet.identifier)")
        NSLog("Dismiss button identifier: \(dismissButton.identifier)")
        
        let startTime = Date()
        
        // Wait for sheet with half the timeout
        guard waitForElement(sheet, timeout: timeout / 2) else {
            NSLog("Sheet did not appear")
            return false
        }
        
        // Wait for animations to complete
        NSLog("Waiting for sheet animation...")
        RunLoop.current.run(mode: .default, before: Date(timeIntervalSinceNow: 1.0))
        
        // Wait for dismiss button with remaining timeout
        guard waitForElementToBeHittable(dismissButton, timeout: timeout / 2) else {
            NSLog("Dismiss button not hittable")
            return false
        }
        
        // Tap dismiss button
        NSLog("Tapping dismiss button...")
        dismissButton.tap()
        
        // Wait for sheet to disappear
        let result = waitForElementToDisappear(sheet, timeout: timeout)
        
        let elapsed = Date().timeIntervalSince(startTime)
        NSLog("Total sheet handling duration: \(String(format: "%.2f", elapsed))s")
        NSLog("Sheet dismissed successfully: \(result)")
        
        return result
    }
    
    private func waitForMinimumPoints(_ label: XCUIElement, minimum: Int = 2, timeout: TimeInterval = 15) -> Bool {
        let startTime = Date()
        let predicate = NSPredicate { (object, _) -> Bool in
            guard let label = object as? XCUIElement,
                  let currentValue = Int(label.label) else {
                NSLog("Failed to parse points count from label: \(String(describing: (object as? XCUIElement)?.label))")
                return false
            }
            NSLog("Current points count: \(currentValue)")
            return currentValue >= minimum
        }
        
        let expectation = XCTNSPredicateExpectation(predicate: predicate, object: label)
        let result = XCTWaiter().wait(for: [expectation], timeout: timeout) == .completed
        
        let elapsed = Date().timeIntervalSince(startTime)
        NSLog("Waited \(String(format: "%.2f", elapsed))s for minimum points")
        
        if !result {
            NSLog("Failed to reach minimum points count")
            NSLog("Current label value: \(label.label)")
            NSLog("Current app hierarchy:")
            debugPrint(app.debugDescription)
        }
        
        return result
    }
    
    private func createTestFarmAndField() throws {
        NSLog("\n=== Starting Farm and Field Creation ===")

        // Open farm management
        let manageFarmsButtonQuery = app.buttons["button.farms.manage"]

        // 1. Find and wait for a known element in the container (farm name label)
        let farmNameLabelElement = app.staticTexts["label.farm.name"].firstMatch
        NSLog("DEBUG: Waiting for farm name label ('label.farm.name') to exist...")
        guard farmNameLabelElement.waitForExistence(timeout: 15) else {
            XCTFail("Farm name label ('label.farm.name') did not exist after 15 seconds. Cannot proceed to find button.")
            return
        }
        NSLog("DEBUG: Farm name label exists. Now trying direct button query.")
        
        // 2. Now attempt direct query for the button by identifier
        // Commented out due to truncation issues
        // let manageFarmsButtonQuery = app.buttons["button.farms.manage"]
        
        // 3. Explicitly wait for the button to exist using its identifier
        NSLog("DEBUG: Waiting for manage farms button ('button.farms.manage') to exist via direct query...")
        guard manageFarmsButtonQuery.firstMatch.waitForExistence(timeout: 15) else {
            NSLog("DEBUG: Direct query failed to find 'button.farms.manage' after 15s. Listing all buttons:")
            app.buttons.allElementsBoundByIndex.forEach { button in
                NSLog("- Button Identifier: \(button.identifier), Label: \(button.label)")
            }
            XCTFail("Button with identifier 'button.farms.manage' did not exist via direct query after 15 seconds")
            return // Exit if button doesn't exist
        }
        NSLog("DEBUG: Manage farms button found via direct query.")
        
        // 4. Get the actual button element
        let manageFarmsButton = manageFarmsButtonQuery.firstMatch
        
        // 5. Wait for the button to be hittable and tap it
        XCTAssertTrue(waitForElementToBeHittable(manageFarmsButton, timeout: defaultTimeout), "Manage farms button was found but not hittable")
        manageFarmsButton.tap()
        // --- End Revert to Direct Query ---

        // --- Wait for Farm Management View (presented as sheet) ---
        // Query for the NavigationView *inside* the sheet, which has the identifier.
        let farmManagementView = app.otherElements["sheet.farmfield.management"] // Changed from app.sheets
        XCTAssertTrue(waitForElement(farmManagementView, timeout: defaultTimeout), "Farm management view ('sheet.farmfield.management' Other) did not appear after tapping manage button.")
        NSLog("DEBUG: Farm Management view ('sheet.farmfield.management' Other) appeared.")

        // Now query elements *within* the sheet
        // Add a new farm
        let addFarmButton = farmManagementView.buttons["button.farm.add"]
        XCTAssertTrue(waitForElementToBeHittable(addFarmButton, timeout: defaultTimeout), "Add farm button not found or not hittable in sheet")
        addFarmButton.tap()

        // --- Wait for Add Farm View (presented as sheet) ---
        // The Add Farm view also contains a NavigationView with the identifier.
        let addFarmView = app.otherElements["sheet.farm"] // Changed from app.sheets
        XCTAssertTrue(waitForElement(addFarmView, timeout: defaultTimeout), "Add Farm view ('sheet.farm' Other) not found")
        let farmNameField = addFarmView.textFields["input.farm.name"] // Query relative to addFarmView
        XCTAssertTrue(fillTextField(farmNameField, withText: "Test Farm"), "Failed to fill farm name")

        // Find and tap the save button within the Add Farm view
        let saveFarmButton = addFarmView.buttons["button.farm.save"] // Query relative to addFarmView
        XCTAssertTrue(waitForElementToBeHittable(saveFarmButton, timeout: defaultTimeout), "Save farm button not hittable") // Uses default
        saveFarmButton.tap()

        // Wait for the Add Farm view to dismiss by checking for an element in the parent view
        // Instead of waiting for disappearance, wait for an element in the parent sheet to exist/be hittable again.
        NSLog("DEBUG: Tapped Save Farm. Waiting for Add Field button in parent sheet to be hittable...")
        let addFieldButtonCheck = farmManagementView.buttons["button.field.add"]
        XCTAssertTrue(waitForElementToBeHittable(addFieldButtonCheck, timeout: 10), "Parent sheet (Farm Management) did not become active after saving farm.")
        NSLog("DEBUG: Add Farm view likely dismissed, parent sheet is active.")
        
        // Debug: Print hierarchy before checking for farm row
        NSLog("DEBUG: Farm Management Sheet Hierarchy Before Farm Check:\n%@", farmManagementView.debugDescription)

        // Verify the farm was created and is visible within the Farm Management sheet
        // Query for the CELL containing the static text
        let farmCellQuery = farmManagementView.cells.containing(.staticText, identifier: "Test Farm")
        XCTAssertTrue(farmCellQuery.firstMatch.waitForExistence(timeout: 10), "Failed to find cell containing 'Test Farm' static text.")
        let farmCell = farmCellQuery.firstMatch // Use the cell for interaction
        NSLog("DEBUG: Found cell containing 'Test Farm' static text.")

        // Select the farm if not already selected (check for checkmark *within the cell*)
        // Note: The checkmark might be an image within the cell, sibling to the static text
        if !farmCell.images["checkmark"].exists { // Check for checkmark image within the cell
            farmCell.tap() // Tap the cell itself
            // Add a short pause after tapping to ensure selection updates
            sleep(1) 
        }

        // Add a new field within the Farm Management sheet
        let addFieldButton = farmManagementView.buttons["button.field.add"]
        XCTAssertTrue(waitForElementToBeHittable(addFieldButton, timeout: defaultTimeout), "Add field button not hittable in sheet")
        addFieldButton.tap()

        // --- Wait for Add Field View (presented as sheet) ---
        // Querying based on the expected runtime identifier pattern ('sheet.field').
        let addFieldView = app.otherElements["sheet.field"] // Changed from otherElements["view.addfield"]
        XCTAssertTrue(waitForElement(addFieldView, timeout: defaultTimeout), "Add Field view ('sheet.field' Other) not found")
        let fieldNameField = addFieldView.textFields["input.field.name"] // Query relative to addFieldView
        XCTAssertTrue(fillTextField(fieldNameField, withText: "Test Field"), "Failed to fill field name")

        // Find and tap the save button within the Add Field view
        let saveFieldButton = addFieldView.buttons["button.field.save"] // Query relative to addFieldView
        XCTAssertTrue(waitForElementToBeHittable(saveFieldButton, timeout: defaultTimeout), "Save field button not found or not hittable") // Uses default
        saveFieldButton.tap()

        // Wait for the Add Field view to dismiss by checking for the Done button in the parent view
        NSLog("DEBUG: Tapped Save Field. Waiting for Done button in parent sheet to be hittable...")
        let doneButtonCheck = farmManagementView.buttons["button.done"]
        XCTAssertTrue(waitForElementToBeHittable(doneButtonCheck, timeout: 10), "Parent sheet (Farm Management) Done button did not become active after saving field.")
        NSLog("DEBUG: Add Field view likely dismissed, parent sheet is active.")
        
        // Debug: Print hierarchy before checking for field row
        NSLog("DEBUG: Farm Management Sheet Hierarchy Before Field Check:\n%@", farmManagementView.debugDescription)

        // Verify the field was created and is visible within the Farm Management sheet
        // Query for the CELL containing the static text
        let fieldCellQuery = farmManagementView.cells.containing(.staticText, identifier: "Test Field")
        // Reduced timeout for list update
        XCTAssertTrue(fieldCellQuery.firstMatch.waitForExistence(timeout: 10), "Failed to find cell for 'Test Field'.") 
        NSLog("DEBUG: Found cell for 'Test Field'.")

        // Return to Record view by tapping Done within the Farm Management sheet
        let doneButton = farmManagementView.buttons["button.done"]
        XCTAssertTrue(waitForElementToBeHittable(doneButton, timeout: defaultTimeout), "Done button not hittable in sheet")
        doneButton.tap()

        // Wait for the Farm Management sheet to dismiss by waiting for the button that opened it to become hittable again
        NSLog("DEBUG: Tapped Done. Waiting for 'Manage Farms' button in parent view to be hittable again...")
        let manageFarmsButtonCheck = app.buttons["button.farms.manage"] // Use the button from the Record view
        XCTAssertTrue(waitForElementToBeHittable(manageFarmsButtonCheck, timeout: 10), "Record View ('Manage Farms' button) did not become active after dismissing Farm Management sheet.")
        NSLog("DEBUG: Farm Management sheet likely dismissed, parent view (Record View) is active.")

        // Verify we're back on the Record view and farm/field names are displayed
        let farmNameLabel = app.staticTexts["label.farm.name"].firstMatch
        let fieldNameLabel = app.staticTexts["label.field.name"].firstMatch

        XCTAssertTrue(waitForElement(farmNameLabel, timeout: defaultTimeout), "Farm name not displayed on Record view")
        XCTAssertTrue(waitForElement(fieldNameLabel, timeout: defaultTimeout), "Field name not displayed on Record view")

        XCTAssertEqual(farmNameLabel.label, "Test Farm", "Farm name on Record view does not match")
        XCTAssertEqual(fieldNameLabel.label, "Test Field", "Field name on Record view does not match")

        NSLog("=== Farm and Field Creation Completed Successfully ===\n")
    }
    
    // MARK: - Navigation Tests
    
    func testBasicNavigation() {
        // --- Test Setup ---
        // App launched in setUp
        // Verify GPS is ready (part of standard setup)

        // --- Test Execution ---
        // 1. Check if the initial view (Record view) is present by checking its Navigation Bar title
        NSLog("DEBUG: Checking for Record View Navigation Bar")
        let recordNavigationBar = app.navigationBars["Record"]
        XCTAssertTrue(recordNavigationBar.waitForExistence(timeout: defaultTimeout), "Record view Navigation Bar ('Record') did not appear.")
        NSLog("DEBUG: Record View Navigation Bar found.")

        // 2. Tap Map Tab (using label)
        let mapTabButton = app.tabBars.buttons["Map"] // Query by label
        XCTAssertTrue(waitForElementToBeHittable(mapTabButton, timeout: defaultTimeout), "Map tab button not hittable")
        mapTabButton.tap()
        NSLog("DEBUG: Tapped Map tab.")

        // 3. Check if Map view is present by checking its Navigation Bar title
        NSLog("DEBUG: Checking for Map View Navigation Bar")
        let mapNavigationBar = app.navigationBars["Field Map"] // Corrected title
        XCTAssertTrue(mapNavigationBar.waitForExistence(timeout: defaultTimeout), "Map view Navigation Bar ('Field Map') did not appear.")
        NSLog("DEBUG: Map View Navigation Bar found.")

        // 4. Tap Export Tab (using label)
        let exportTabButton = app.tabBars.buttons["Export"] // Query by label
        XCTAssertTrue(waitForElementToBeHittable(exportTabButton, timeout: defaultTimeout), "Export tab button not hittable")
        exportTabButton.tap()
        NSLog("DEBUG: Tapped Export tab.")

        // 5. Check if Export view is present by checking its Navigation Bar title
        NSLog("DEBUG: Checking for Export View Navigation Bar")
        let exportNavigationBar = app.navigationBars["Export GPS Data"] // Query by Nav Bar Title
        XCTAssertTrue(exportNavigationBar.waitForExistence(timeout: defaultTimeout), "Export view Navigation Bar ('Export GPS Data') did not appear.")
        NSLog("DEBUG: Export View Navigation Bar found.")

        // 6. Tap Settings Tab (using label)
        let settingsTabButton = app.tabBars.buttons["Settings"] // Query by label
        XCTAssertTrue(waitForElementToBeHittable(settingsTabButton, timeout: defaultTimeout), "Settings tab button not hittable")
        settingsTabButton.tap()
        NSLog("DEBUG: Tapped Settings tab.")

        // 7. Check if Settings view is present by checking its Navigation Bar title
        NSLog("DEBUG: Checking for Settings View Navigation Bar")
        let settingsNavigationBar = app.navigationBars["Settings"] // Query by Nav Bar Title
        XCTAssertTrue(settingsNavigationBar.waitForExistence(timeout: defaultTimeout), "Settings view Navigation Bar ('Settings') did not appear.")
        NSLog("DEBUG: Settings View Navigation Bar found.")

        // 8. Tap Record Tab again (using label)
        let recordTabButton = app.tabBars.buttons["Record"] // Query by label
        XCTAssertTrue(waitForElementToBeHittable(recordTabButton, timeout: defaultTimeout), "Record tab button not hittable (second time)")
        recordTabButton.tap()
        NSLog("DEBUG: Tapped Record tab again.")

        // 9. Verify Record view is present again by checking Navigation Bar title
        NSLog("DEBUG: Checking for Record View Navigation Bar again")
        XCTAssertTrue(recordNavigationBar.waitForExistence(timeout: defaultTimeout), "Record view Navigation Bar ('Record') did not reappear.")
        NSLog("DEBUG: Record View Navigation Bar found again.")

        // --- Test Completion ---
        NSLog("DEBUG: testBasicNavigation completed successfully.")
    }
    
    // MARK: - Farm and Field Management Tests
    
    func testFarmFieldCreation() throws {
        NSLog("\n=== Starting testFarmFieldCreation ===")

        // --- Ensure Record Tab is Selected --- 
        // This also implicitly waits for the TabBar to exist.
        let tabBar = app.tabBars.firstMatch
        XCTAssertTrue(tabBar.waitForExistence(timeout: defaultTimeout), "TabBar does not exist.")

        let recordTabButton = tabBar.buttons["Record"]
        // Always tap record tab to ensure correct view is active
        if recordTabButton.exists {
            NSLog("DEBUG: Tapping Record tab button to ensure view is active.")
            recordTabButton.tap()
            sleep(1) // Short pause after tab tap
        } else {
            XCTFail("Record Tab button does not exist.")
            return
        }

        // --- Call the helper function to perform the core logic ---
        // The helper now assumes it starts on the Record View and handles its own element finding.
        try createTestFarmAndField()

        // --- Final Verification (already done in helper, but can keep for clarity) ---
        // Verify names on Record view after returning from helper
        let farmNameLabel = app.staticTexts["label.farm.name"].firstMatch
        let fieldNameLabel = app.staticTexts["label.field.name"].firstMatch

        XCTAssertTrue(waitForElement(farmNameLabel, timeout: defaultTimeout), "Farm name not displayed on Record view after helper execution")
        XCTAssertTrue(waitForElement(fieldNameLabel, timeout: defaultTimeout), "Field name not displayed on Record view after helper execution")

        XCTAssertEqual(farmNameLabel.label, "Test Farm", "Farm name on Record view does not match after helper execution")
        XCTAssertEqual(fieldNameLabel.label, "Test Field", "Field name on Record view does not match after helper execution")

        NSLog("=== testFarmFieldCreation Completed Successfully ===")
    }
    
    // MARK: - Recording Tests
    
    func testContinuousRecording() throws {
        // NOTE: This test now assumes a farm/field ('Test Farm'/'Test Field') already exists.
        NSLog("\n=== Starting testContinuousRecording ===")

        // Ensure we're on the Record view and the correct farm/field is selected
        XCTAssertTrue(app.tabBars.buttons["Record"].isSelected, "Record tab not selected")
        let farmNameLabel = app.staticTexts["label.farm.name"]
        let fieldNameLabel = app.staticTexts["label.field.name"]
        XCTAssertTrue(farmNameLabel.waitForExistence(timeout: defaultTimeout), "Farm name label not found for continuous recording")
        XCTAssertTrue(fieldNameLabel.waitForExistence(timeout: defaultTimeout), "Field name label not found for continuous recording")
        XCTAssertEqual(farmNameLabel.label, "Test Farm", "Incorrect farm selected for continuous recording")
        XCTAssertEqual(fieldNameLabel.label, "Test Field", "Incorrect field selected for continuous recording")

        // Verify GPS status and wait for it to be ready
        let gpsStatusLabel = app.staticTexts["label.gps.status"]
        XCTAssertTrue(waitForElement(gpsStatusLabel, timeout: defaultTimeout), "GPS status label not found")
        let gpsReadyExpectation = XCTNSPredicateExpectation(
            predicate: NSPredicate(format: "label CONTAINS[c] 'Ready' OR label CONTAINS[c] 'Fix' OR label CONTAINS[c] 'Signal'"), 
            object: gpsStatusLabel
        )
        XCTAssertTrue(XCTWaiter().wait(for: [gpsReadyExpectation], timeout: 15) == .completed, "GPS did not become ready")
        
        // Get initial points count
        let pointsCountLabel = app.staticTexts["label.points.count"]
        XCTAssertTrue(waitForElement(pointsCountLabel, timeout: defaultTimeout), "Points count label not found")
        let initialCountText = pointsCountLabel.label
        let initialCount = Int(initialCountText.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) ?? 0
        NSLog("DEBUG: Initial points count: %d", initialCount)

        // Start recording
        let recordButton = app.buttons["button.recording.start"]
        XCTAssertTrue(waitForElementToBeHittable(recordButton, timeout: defaultTimeout), "Start Recording button not hittable")
        recordButton.tap()
        NSLog("DEBUG: Tapped Start Recording button.")

        // Verify button changes to 'Stop Recording'
        let stopButton = app.buttons["button.recording.stop"]
        XCTAssertTrue(waitForElement(stopButton, timeout: defaultTimeout), "Stop Recording button did not appear")
        NSLog("DEBUG: Stop Recording button appeared.")

        // Wait for some points to be recorded (e.g., wait for count to increase by 3)
        let pointsRecordedExpectation = XCTNSPredicateExpectation(
            predicate: NSPredicate { [initialCount] _, _ in
                let currentCountText = pointsCountLabel.label
                if let currentCount = Int(currentCountText.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) {
                    return currentCount >= initialCount + 3
                }
                return false
            },
            object: pointsCountLabel
        )
        NSLog("DEBUG: Waiting for at least 3 points to be recorded...")
        XCTAssertTrue(XCTWaiter().wait(for: [pointsRecordedExpectation], timeout: 15) == .completed, "Did not record at least 3 points within 15 seconds")
        let pointsRecordedCount = (Int(pointsCountLabel.label.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) ?? initialCount) - initialCount
        NSLog("DEBUG: Recorded %d points.", pointsRecordedCount)

        // Stop recording
        XCTAssertTrue(waitForElementToBeHittable(stopButton, timeout: defaultTimeout), "Stop Recording button not hittable")
        stopButton.tap()
        NSLog("DEBUG: Tapped Stop Recording button.")

        // Verify button changes back to 'Start Recording'
        XCTAssertTrue(waitForElement(recordButton, timeout: defaultTimeout), "Start Recording button did not reappear")
        NSLog("DEBUG: Start Recording button reappeared.")

        // Verify final point count
        let finalCountText = pointsCountLabel.label
        let finalCount = Int(finalCountText.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) ?? initialCount
        XCTAssertGreaterThanOrEqual(finalCount, initialCount + 3, "Final point count did not increase sufficiently")

        // Verification of points in a list is removed as the list UI is no longer present.
        NSLog("DEBUG: List verification skipped as list view is obsolete.")
        NSLog("=== testContinuousRecording Completed Successfully ===")
    }
    
    // MARK: - Export Tests
    
    func testExportFunction() throws {
        NSLog("\n=== Starting testExportFunction ===\n")

        // Ensure we are on the Record tab initially
        let recordTab = app.tabBars.buttons["Record"]
        if !recordTab.isSelected {
            recordTab.tap()
        }

        // Check if points exist before running export
        let pointsCountLabel = app.staticTexts["label.points.count"]
        XCTAssertTrue(pointsCountLabel.waitForExistence(timeout: defaultTimeout), "Points count label did not appear.")

        let initialPointsCountString = pointsCountLabel.label
        let initialPointsCount = Int(initialPointsCountString) ?? 0
        NSLog("DEBUG: Initial points count = \(initialPointsCount)")

        if initialPointsCount == 0 {
            NSLog("DEBUG: No points found. Running testMarkSpot() to create a point...")
            // Call testMarkSpot to ensure at least one point exists
            try testMarkSpot()
            // Re-check points count after marking spot (optional but good practice)
            XCTAssertTrue(pointsCountLabel.waitForExistence(timeout: defaultTimeout), "Points count label did not reappear after marking spot.")
            let pointsCountAfterMark = Int(pointsCountLabel.label) ?? 0
            NSLog("DEBUG: Points count after marking spot = \(pointsCountAfterMark)")
            XCTAssertGreaterThan(pointsCountAfterMark, 0, "Points count did not increase after running testMarkSpot.")
        } else {
            NSLog("DEBUG: Points already exist (\(initialPointsCount)). Proceeding directly to export.")
        }

        // Now navigate to the Export tab
        let exportTab = app.tabBars.buttons["Export"]
        XCTAssertTrue(waitForElementToBeHittable(exportTab, timeout: defaultTimeout), "Export tab button not hittable")
        exportTab.tap()
        NSLog("DEBUG: Navigated to Export tab.")

        // Verify points count is > 0 on Record view before navigating away
        let pointsReadyExpectation = XCTNSPredicateExpectation(
            predicate: NSPredicate { _, _ in 
                let count = Int(pointsCountLabel.label.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) ?? 0
                return count > 0
            },
            object: pointsCountLabel
        )
        // Use XCTWaiter.wait directly
        XCTAssertTrue(XCTWaiter.wait(for: [pointsReadyExpectation], timeout: 5) == .completed, "Point count did not become > 0 after recording.")
        NSLog("DEBUG: Verified points exist on Record view.")

        // Verify correct farm/field shown (based on setup)
        XCTAssertEqual(app.staticTexts["label.export.selectedfarm"].label, "Test Farm", "Incorrect farm shown on Export view")
        XCTAssertEqual(app.staticTexts["label.export.selectedfield"].label, "Test Field", "Incorrect field shown on Export view")

        // Tap the main export button
        let exportDataButton = app.buttons["button.export.data"]
        XCTAssertTrue(waitForElementToBeHittable(exportDataButton, timeout: defaultTimeout), "Export Data button not hittable")
        exportDataButton.tap()
        NSLog("DEBUG: Tapped Export Data button.")

        // Wait for the Share Sheet
        let shareSheet = app.sheets.firstMatch
        XCTAssertTrue(shareSheet.waitForExistence(timeout: 10), "Share sheet did not appear after tapping Export Data")
        NSLog("DEBUG: Share sheet appeared.")

        // Verify expected elements in the Share Sheet (Optional - can be brittle)
        // Example: Check for file name inclusion
        // Check navigation bar within the sheet
        XCTAssertTrue(shareSheet.navigationBars.firstMatch.waitForExistence(timeout: defaultTimeout), "Share sheet navigation bar not found")
        let filenamePredicate = NSPredicate(format: "label CONTAINS[c] %@", "Test_Farm_Test_Field") // Based on generateFileName logic
        let filenameText = shareSheet.staticTexts.containing(filenamePredicate).firstMatch
        XCTAssertTrue(filenameText.waitForExistence(timeout: defaultTimeout), "Share sheet does not seem to contain expected filename part: Test_Farm_Test_Field")
        NSLog("DEBUG: Share sheet seems to contain expected filename part: Test_Farm_Test_Field")

        // Dismiss the share sheet (essential for test continuation)
        // Prioritize 'Close' or 'Cancel', fallback to tapping outside
        let closeButton = shareSheet.buttons["Close"]
        let cancelButton = shareSheet.buttons["Cancel"]

        if closeButton.isHittable {
            closeButton.tap()
            NSLog("DEBUG: Tapped Share Sheet 'Close' button.")
        } else if cancelButton.isHittable {
             cancelButton.tap()
             NSLog("DEBUG: Tapped Share Sheet 'Cancel' button.")
        } else {
            // Fallback: Tap slightly below the top edge to dismiss
            let coordinate = app.coordinate(withNormalizedOffset: CGVector(dx: 0.5, dy: 0.1))
            coordinate.tap()
            NSLog("DEBUG: Attempted to tap outside Share Sheet to dismiss.")
            sleep(1) // Give dismissal animation time
        }
        
        // Verify sheet disappeared by checking the main export button is hittable again
        XCTAssertTrue(waitForElementToBeHittable(exportDataButton, timeout: defaultTimeout), "Export Data button did not become hittable after share sheet dismissal")
        NSLog("DEBUG: Share sheet dismissed.")

        NSLog("=== testExportFunction Completed Successfully ===")
    }
    
    // MARK: - Settings Tests

    private func fillTextField(_ textField: XCUIElement, withText text: String) -> Bool {
        NSLog("\n=== Filling Text Field ===")
        NSLog("Text field identifier: \(textField.identifier)")
        NSLog("Text to enter: \(text)")
        
        guard waitForElementToBeHittable(textField, timeout: defaultTimeout) else {
            NSLog("Text field not hittable")
            return false
        }
        
        textField.tap()
        textField.clearText()
        textField.typeText(text)
        
        // Verify the text was entered correctly
        if let currentValue = textField.value as? String {
            NSLog("Current text field value: \(currentValue)")
            return currentValue == text
        } else {
            NSLog("Could not verify text field value")
            return false
        }
    }
    
    // MARK: - Debugging Helpers
    
    private func logAllElements(in element: XCUIElement = XCUIApplication()) {
        NSLog("\n--- Logging Elements Hierarchy Start ---")
        func logElement(_ element: XCUIElement, level: Int) {
            let indent = String(repeating: "  ", count: level)
            let frameString = String(format: "{{%.1f, %.1f}, {%.1f, %.1f}}", 
                                     element.frame.origin.x, element.frame.origin.y, 
                                     element.frame.size.width, element.frame.size.height)
            NSLog("%@Type: %@, ID: \"%@\", Label: \"%@\", Frame: %@, Exists: %d, Hittable: %d", 
                  indent, String(describing: element.elementType), element.identifier, element.label, frameString, element.exists, element.isHittable)
            
            // Recursively log children, limit depth to avoid excessive logs if needed
            if level < 10 { // Adjust depth limit as necessary
                for i in 0..<element.children(matching: .any).count {
                    logElement(element.children(matching: .any).element(boundBy: i), level: level + 1)
                }
            }
        }
        
        logElement(element, level: 0)
        NSLog("--- Logging Elements Hierarchy End ---\n")
    }

    // MARK: - Test Map Interaction
    func testMapInteraction() throws {
        NSLog("\n=== Starting testMapInteraction ===")
        // Assumption: Test runs with a pre-selected farm and field.
        // Verify Record view is active and farm/field labels are present.
        let recordNavBar = app.navigationBars["Record"]
        XCTAssertTrue(recordNavBar.waitForExistence(timeout: defaultTimeout), "Record view did not appear initially for Map test.")
        XCTAssertTrue(app.staticTexts["label.farm.name"].waitForExistence(timeout: defaultTimeout), "Farm name label not found.")
        XCTAssertTrue(app.staticTexts["label.field.name"].waitForExistence(timeout: defaultTimeout), "Field name label not found.")
        NSLog("DEBUG: Test assumes setup complete, on Record view with a pre-selected farm/field.")

        // Ensure at least one point exists for mapping
        let pointsCountLabel = app.staticTexts["label.points.count"]
        XCTAssertTrue(waitForElement(pointsCountLabel, timeout: defaultTimeout), "Points count label not found.")
        
        let initialCountText = pointsCountLabel.label
        let initialCount = Int(initialCountText.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) ?? 0
        NSLog("DEBUG: Initial points count for map test: %d", initialCount)

        if initialCount == 0 {
            NSLog("DEBUG: No points found. Performing Mark Spot action...")
            // 1. Tap the Mark Spot button
            let markSpotButton = app.buttons["button.point.save"]
            XCTAssertTrue(waitForElementToBeHittable(markSpotButton, timeout: defaultTimeout), "Mark Spot button not hittable for initial point creation")
            markSpotButton.tap()
            NSLog("DEBUG: Tapped Mark Spot button (for setup).")

            // 2. Wait for the Mark Spot Note View sheet
            let markSpotSheet = app.otherElements["view.markspot"]
            XCTAssertTrue(markSpotSheet.waitForExistence(timeout: defaultTimeout), "Mark Spot Note View ('view.markspot') did not appear (for setup).")
            NSLog("DEBUG: Mark Spot Note View sheet appeared (for setup).")

            // 3. Tap Save (using default point type/tag)
            let saveButton = markSpotSheet.buttons["button.savepoint"]
            XCTAssertTrue(waitForElementToBeHittable(saveButton, timeout: defaultTimeout), "Save button in Mark Spot sheet not hittable (for setup)")
            saveButton.tap()
            NSLog("DEBUG: Tapped Save button in Mark Spot sheet (for setup).")

            // 4. Verify the sheet dismisses by waiting for the main Record view to become active
            let manageFarmsButtonCheck = app.buttons["button.farms.manage"]
            XCTAssertTrue(waitForElementToBeHittable(manageFarmsButtonCheck, timeout: defaultTimeout), "Record View ('Manage Farms' button) did not become active after saving spot (for setup).")
            NSLog("DEBUG: Mark Spot sheet dismissed, Record View is active (for setup).")
            
            // Now, wait for points count to become > 0 (should update quickly after sheet dismiss)
            let pointsRecordedExpectation = XCTNSPredicateExpectation(
                predicate: NSPredicate { _, _ in 
                    let currentCountText = pointsCountLabel.label
                    let currentCount = Int(currentCountText.components(separatedBy: CharacterSet.decimalDigits.inverted).joined()) ?? 0
                    return currentCount > 0
                },
                object: pointsCountLabel
            )
            XCTAssertTrue(XCTWaiter.wait(for: [pointsRecordedExpectation], timeout: 5) == .completed, "Point count did not become > 0 after marking spot.")
            NSLog("DEBUG: Mark Spot action complete, point count > 0.")
        } else {
            NSLog("DEBUG: Points already exist (Count: %d). Skipping quick recording.", initialCount)
        }

        // Navigate to Map Tab (using label)
        let mapTabButton = app.tabBars.buttons["Map"]
        XCTAssertTrue(waitForElementToBeHittable(mapTabButton, timeout: defaultTimeout), "Map tab button not hittable")
        mapTabButton.tap()

        // Verify Map View is visible (check Nav Bar title)
        let mapNavBar = app.navigationBars["Field Map"]
        XCTAssertTrue(mapNavBar.waitForExistence(timeout: defaultTimeout), "Map view Nav Bar ('Field Map') did not appear")
        // We can also check for the map element itself for extra certainty
        let mapViewElement = app.otherElements["view.map"]
        XCTAssertTrue(waitForElement(mapViewElement, timeout: defaultTimeout), "Map view container (view.map) did not appear")
        // Query for the first Map element within the container instead of relying on identifier
        let map = mapViewElement.maps.firstMatch
        XCTAssertTrue(waitForElement(map, timeout: defaultTimeout), "MapKit map element (first match) not found within view.map")
        NSLog("DEBUG: Navigated to Map view and found map element.")

        // --- Interact with Map Style Menu ---
        // 1. Find and tap the menu button (query relative to the Navigation Bar)
        let mapStyleMenuButton = mapNavBar.buttons["menu.mapstyle"]
        XCTAssertTrue(waitForElementToBeHittable(mapStyleMenuButton, timeout: defaultTimeout), "Map Style menu button (menu.mapstyle) not hittable in Nav Bar")
        mapStyleMenuButton.tap()
        NSLog("DEBUG: Tapped Map Style menu button.")

        // 2. Select Standard style (Simplified: Only test one style change)
        let standardStyleButton = app.buttons["button.mapstyle.standard"]
        XCTAssertTrue(waitForElementToBeHittable(standardStyleButton, timeout: defaultTimeout), "Standard map style button not hittable in menu")
        standardStyleButton.tap()
        NSLog("DEBUG: Selected Standard map style.")

        // Steps 3-5 (tapping menu/hybrid/menu again) are removed for simplification.
        
        // Step 6 (Imagery) is already skipped.
        NSLog("DEBUG: Skipping further map style checks for simplification.")
        
        // Add a small delay for UI to settle after closing the menu
        sleep(1)
        NSLog("DEBUG: Waited 1s after selecting map style.")
        
        // Log hierarchy before attempting to tap the tab bar
        NSLog("DEBUG: Logging app hierarchy before tapping Record tab...")
        logAllElements(in: app)

        // Return to Record view - Tapping another tab first to reset state
        let exportTabButton = app.tabBars.buttons["Export"]
        XCTAssertTrue(waitForElementToBeHittable(exportTabButton, timeout: defaultTimeout), "Export tab button not hittable")
        exportTabButton.tap()
        NSLog("DEBUG: Tapped Export tab to reset UI state.")
        Thread.sleep(forTimeInterval: 0.5) // Short pause for tab switch (using Thread.sleep for Double)
        
        let recordTabButton = app.tabBars.buttons["Record"]
        XCTAssertTrue(waitForElementToBeHittable(recordTabButton, timeout: defaultTimeout), "Record tab button not hittable after switching to Export")
        recordTabButton.tap()
        NSLog("DEBUG: Tapped Record tab.")
        
        // Verify by checking Nav Bar title
        XCTAssertTrue(app.navigationBars["Record"].waitForExistence(timeout: defaultTimeout), "Record view Nav Bar did not reappear after map test.")

        NSLog("testMapInteraction completed successfully.")
    }

    // MARK: - Test Mark Spot Functionality
    func testMarkSpot() throws {
        NSLog("\n=== Starting testMarkSpot ===")
        // --- Test Setup ---
        // Assumption: Test runs with a pre-selected farm and field.
        // Verify Record view is active and farm/field labels are present.
        let recordNavBar = app.navigationBars["Record"]
        XCTAssertTrue(recordNavBar.waitForExistence(timeout: defaultTimeout), "Record view did not appear initially for Mark Spot test.")
        XCTAssertTrue(app.staticTexts["label.farm.name"].waitForExistence(timeout: defaultTimeout), "Farm name label not found.")
        XCTAssertTrue(app.staticTexts["label.field.name"].waitForExistence(timeout: defaultTimeout), "Field name label not found.")
        NSLog("DEBUG: Test assumes setup complete, on Record view with a pre-selected farm/field.")

        // --- Test Execution ---
        // 1. Tap the Mark Spot button
        let markSpotButton = app.buttons["button.point.save"]
        XCTAssertTrue(waitForElementToBeHittable(markSpotButton, timeout: defaultTimeout), "Mark Spot button not hittable")
        markSpotButton.tap()
        NSLog("DEBUG: Tapped Mark Spot button.")

        // 2. Wait for the Mark Spot Note View sheet
        let markSpotSheet = app.otherElements["view.markspot"]
        XCTAssertTrue(markSpotSheet.waitForExistence(timeout: defaultTimeout), "Mark Spot Note View ('view.markspot') did not appear.")
        NSLog("DEBUG: Mark Spot Note View sheet appeared.")

        // 3. Select "Boundary Point" type (using segmentedControl query)
        let pointTypePicker = markSpotSheet.segmentedControls["picker.pointtype"] // Query as segmentedControl
        XCTAssertTrue(pointTypePicker.waitForExistence(timeout: defaultTimeout), "Point type segmented control (picker.pointtype) not found")
        
        let boundaryTypeButton = pointTypePicker.buttons["Boundary Point"] // Button within the control
        XCTAssertTrue(waitForElementToBeHittable(boundaryTypeButton, timeout: defaultTimeout), "Boundary Point type button not hittable")
        
        if !boundaryTypeButton.isSelected {
            boundaryTypeButton.tap()
            NSLog("DEBUG: Selected Boundary Point type (was not default).")
            sleep(1) // Allow tag options to update if needed
        } else {
             NSLog("DEBUG: Boundary Point type already selected (default).")
        }
       
        // Steps 4 & 5 (Menu interaction) removed. Assuming entering notes selects 'Custom Note...' tag.

        // 4. Enter text into the notes field (TextField)
        let freeTextNoteField = markSpotSheet.textFields["input.freetextnote"] 
        XCTAssertTrue(waitForElementToBeHittable(freeTextNoteField, timeout: defaultTimeout), "Notes text field (input.freetextnote TextField) not hittable")
        freeTextNoteField.tap()
        freeTextNoteField.typeText("Free text notes for boundary marker.")
        NSLog("DEBUG: Entered text into notes field.")

        // 4.5 Verify Tag changed to 'Custom Note...' after entering text
        let tagMenuAfterNotes = markSpotSheet.buttons["menu.tag"]
        // Add a small wait for the UI to potentially update after text entry
        sleep(1) 
        // We query the label directly for comparison
        XCTAssertEqual(tagMenuAfterNotes.label, "Custom Note...", "Tag menu label did not update to 'Custom Note...' after typing in notes field.")
        NSLog("DEBUG: Verified tag menu label updated to 'Custom Note...'.")

        // 5. Tap Save
        let saveButton = markSpotSheet.buttons["button.savepoint"]
        XCTAssertTrue(waitForElementToBeHittable(saveButton, timeout: defaultTimeout), "Save button in Mark Spot sheet not hittable")
        saveButton.tap()
        NSLog("DEBUG: Tapped Save button in Mark Spot sheet.")

        // 6. Verify the sheet dismisses
        NSLog("DEBUG: Waiting for Record View ('Manage Farms' button) to become active again...")
        let manageFarmsButtonCheck = app.buttons["button.farms.manage"]
        XCTAssertTrue(waitForElementToBeHittable(manageFarmsButtonCheck, timeout: 10), "Record View ('Manage Farms' button) did not become active after saving spot.")
        NSLog("DEBUG: Mark Spot sheet dismissed, Record View is active.")

        // --- Test Completion ---
        NSLog("=== testMarkSpot Completed Successfully ===")
    }
}
