import Foundation

struct KMLExporter {
    
    // Export a single field to KML format
    static func exportField(_ field: Field) -> String {
        let header = """
        <?xml version="1.0" encoding="UTF-8"?>
        <kml xmlns="http://www.opengis.net/kml/2.2">
        <Document>
            <name>\(field.name)</name>
            <description>Field exported from CropCompass GPS</description>
            <Style id="continuousPointStyle">
                <IconStyle>
                    <color>ff0000ff</color>
                    <scale>0.8</scale>
                    <Icon>
                        <href>http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png</href>
                    </Icon>
                </IconStyle>
            </Style>
            <Style id="markedPointStyle">
                <IconStyle>
                    <color>ff00ffff</color>
                    <scale>1.0</scale>
                    <Icon>
                        <href>http://maps.google.com/mapfiles/kml/pushpin/ylw-pushpin.png</href>
                    </Icon>
                </IconStyle>
            </Style>
            <Style id="boundaryPointStyle">
                <IconStyle>
                    <color>ff00ff00</color>
                    <scale>1.0</scale>
                    <Icon>
                        <href>http://maps.google.com/mapfiles/kml/shapes/square.png</href>
                    </Icon>
                </IconStyle>
            </Style>
            <Style id="lineStyle">
                <LineStyle>
                    <color>ff0000ff</color>
                    <width>3</width>
                </LineStyle>
            </Style>
            <Style id="polygonStyle">
                <LineStyle>
                    <color>ff0000ff</color>
                    <width>3</width>
                </LineStyle>
                <PolyStyle>
                    <color>400000ff</color>
                </PolyStyle>
            </Style>
        """
        
        // Add placemarks for each point
        var placemarks = ""
        for point in field.geoPoints {
            let styleId = switch point.pointType {
                case .continuous: "continuousPointStyle"
                case .marked: "markedPointStyle"
                case .boundary: "boundaryPointStyle"
            }
            
            let timeString = ISO8601DateFormatter().string(from: point.timestamp)
            let description = point.note ?? "Point recorded at \(timeString)"
            
            placemarks += """
            
            <Placemark>
                <name>\(point.pointType.rawValue.capitalized) Point</name>
                <description><![CDATA[\(description)]]></description>
                <TimeStamp>
                    <when>\(timeString)</when>
                </TimeStamp>
                <styleUrl>#\(styleId)</styleUrl>
                <Point>
                    <coordinates>\(point.longitude),\(point.latitude),\(point.altitude ?? 0)</coordinates>
                </Point>
            </Placemark>
            """
        }
        
        // Add LineString for the track
        let lineString = """
        
        <Placemark>
            <name>\(field.name) Track</name>
            <description>Track line for \(field.name)</description>
            <styleUrl>#lineStyle</styleUrl>
            <LineString>
                <tessellate>1</tessellate>
                <coordinates>
                    \(field.geoPoints.map { "\($0.longitude),\($0.latitude),\($0.altitude ?? 0)" }.joined(separator: "\n                    "))
                </coordinates>
            </LineString>
        </Placemark>
        """
        
        // Add Polygon if we have at least 3 points
        var polygon = ""
        if field.geoPoints.count >= 3 {
            polygon = """
            
            <Placemark>
                <name>\(field.name) Area</name>
                <description>Field area for \(field.name)</description>
                <styleUrl>#polygonStyle</styleUrl>
                <Polygon>
                    <tessellate>1</tessellate>
                    <outerBoundaryIs>
                        <LinearRing>
                            <coordinates>
                                \(field.geoPoints.map { "\($0.longitude),\($0.latitude),\($0.altitude ?? 0)" }.joined(separator: "\n                                "))
                                \(field.geoPoints.first.map { "\($0.longitude),\($0.latitude),\($0.altitude ?? 0)" } ?? "")
                            </coordinates>
                        </LinearRing>
                    </outerBoundaryIs>
                </Polygon>
            </Placemark>
            """
        }
        
        let footer = """
        
        </Document>
        </kml>
        """
        
        return header + placemarks + lineString + polygon + footer
    }
    
    // Export multiple fields
    static func exportFields(_ fields: [Field]) -> String {
        let header = """
        <?xml version="1.0" encoding="UTF-8"?>
        <kml xmlns="http://www.opengis.net/kml/2.2">
        <Document>
            <name>CropCompass GPS Export</name>
            <description>Multiple fields exported from CropCompass GPS</description>
            <Style id="continuousPointStyle">
                <IconStyle>
                    <color>ff0000ff</color>
                    <scale>0.8</scale>
                    <Icon>
                        <href>http://maps.google.com/mapfiles/kml/shapes/placemark_circle.png</href>
                    </Icon>
                </IconStyle>
            </Style>
            <Style id="markedPointStyle">
                <IconStyle>
                    <color>ff00ffff</color>
                    <scale>1.0</scale>
                    <Icon>
                        <href>http://maps.google.com/mapfiles/kml/pushpin/ylw-pushpin.png</href>
                    </Icon>
                </IconStyle>
            </Style>
            <Style id="boundaryPointStyle">
                <IconStyle>
                    <color>ff00ff00</color>
                    <scale>1.0</scale>
                    <Icon>
                        <href>http://maps.google.com/mapfiles/kml/shapes/square.png</href>
                    </Icon>
                </IconStyle>
            </Style>
            <Style id="lineStyle">
                <LineStyle>
                    <color>ff0000ff</color>
                    <width>3</width>
                </LineStyle>
            </Style>
            <Style id="polygonStyle">
                <LineStyle>
                    <color>ff0000ff</color>
                    <width>3</width>
                </LineStyle>
                <PolyStyle>
                    <color>400000ff</color>
                </PolyStyle>
            </Style>
        """
        
        var content = ""
        
        for field in fields {
            // Create a folder for each field
            content += """
            
            <Folder>
                <name>\(field.name)</name>
                <description>Field data for \(field.name)</description>
            """
            
            // Add placemarks for each point
            for point in field.geoPoints {
                let styleId = switch point.pointType {
                    case .continuous: "continuousPointStyle"
                    case .marked: "markedPointStyle"
                    case .boundary: "boundaryPointStyle"
                }
                
                let timeString = ISO8601DateFormatter().string(from: point.timestamp)
                let description = point.note ?? "Point recorded at \(timeString)"
                
                content += """
                
                <Placemark>
                    <name>\(point.pointType.rawValue.capitalized) Point</name>
                    <description><![CDATA[\(description)]]></description>
                    <TimeStamp>
                        <when>\(timeString)</when>
                    </TimeStamp>
                    <styleUrl>#\(styleId)</styleUrl>
                    <Point>
                        <coordinates>\(point.longitude),\(point.latitude),\(point.altitude ?? 0)</coordinates>
                    </Point>
                </Placemark>
                """
            }
            
            // Add LineString for the track
            content += """
            
            <Placemark>
                <name>\(field.name) Track</name>
                <description>Track line for \(field.name)</description>
                <styleUrl>#lineStyle</styleUrl>
                <LineString>
                    <tessellate>1</tessellate>
                    <coordinates>
                        \(field.geoPoints.map { "\($0.longitude),\($0.latitude),\($0.altitude ?? 0)" }.joined(separator: "\n                        "))
                    </coordinates>
                </LineString>
            </Placemark>
            """
            
            // Add Polygon if we have at least 3 points
            if field.geoPoints.count >= 3 {
                content += """
                
                <Placemark>
                    <name>\(field.name) Area</name>
                    <description>Field area for \(field.name)</description>
                    <styleUrl>#polygonStyle</styleUrl>
                    <Polygon>
                        <tessellate>1</tessellate>
                        <outerBoundaryIs>
                            <LinearRing>
                                <coordinates>
                                    \(field.geoPoints.map { "\($0.longitude),\($0.latitude),\($0.altitude ?? 0)" }.joined(separator: "\n                                    "))
                                    \(field.geoPoints.first.map { "\($0.longitude),\($0.latitude),\($0.altitude ?? 0)" } ?? "")
                                </coordinates>
                            </LinearRing>
                        </outerBoundaryIs>
                    </Polygon>
                </Placemark>
                """
            }
            
            content += """
            
            </Folder>
            """
        }
        
        let footer = """
        
        </Document>
        </kml>
        """
        
        return header + content + footer
    }
    
    // Save KML data to a temporary file and return the URL
    static func saveKMLToFile(kmlContent: String, fileName: String) -> URL? {
        let tempDirectoryURL = FileManager.default.temporaryDirectory
        let fileURL = tempDirectoryURL.appendingPathComponent(fileName)
        
        do {
            try kmlContent.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Error saving KML file: \(error.localizedDescription)")
            return nil
        }
    }
}
