import Foundation

struct CSVExporter {
    
    // Export a single field to CSV format
    static func exportField(_ field: Field) -> String {
        // CSV header
        let header = "Latitude,Longitude,Elevation,Timestamp,PointType,Note,Heading\n"
        
        // CSV rows for each point
        var rows = ""
        for point in field.geoPoints {
            let dateFormatter = ISO8601DateFormatter()
            let timeString = dateFormatter.string(from: point.timestamp)
            
            // Format note with quotes and escape any quotes inside
            let formattedNote = point.note != nil ? "\"\(point.note!.replacingOccurrences(of: "\"", with: "\"\""))\"" : ""
            
            // Create CSV row
            let row = "\(point.latitude),\(point.longitude),\(point.altitude ?? 0),\(timeString),\(point.pointType.rawValue),\(formattedNote),\(point.heading ?? 0)\n"
            rows += row
        }
        
        return header + rows
    }
    
    // Export multiple fields
    static func exportFields(_ fields: [Field]) -> String {
        // CSV header with field name column
        let header = "FieldName,Latitude,Longitude,Elevation,Timestamp,PointType,Note,Heading\n"
        
        // CSV rows for each point in each field
        var rows = ""
        for field in fields {
            for point in field.geoPoints {
                let dateFormatter = ISO8601DateFormatter()
                let timeString = dateFormatter.string(from: point.timestamp)
                
                // Format field name and note with quotes and escape any quotes inside
                let formattedFieldName = "\"\(field.name.replacingOccurrences(of: "\"", with: "\"\""))\""
                let formattedNote = point.note != nil ? "\"\(point.note!.replacingOccurrences(of: "\"", with: "\"\""))\"" : ""
                
                // Create CSV row
                let row = "\(formattedFieldName),\(point.latitude),\(point.longitude),\(point.altitude ?? 0),\(timeString),\(point.pointType.rawValue),\(formattedNote),\(point.heading ?? 0)\n"
                rows += row
            }
        }
        
        return header + rows
    }
    
    // Save CSV data to a temporary file and return the URL
    static func saveCSVToFile(csvContent: String, fileName: String) -> URL? {
        let tempDirectoryURL = FileManager.default.temporaryDirectory
        let fileURL = tempDirectoryURL.appendingPathComponent(fileName)
        
        do {
            try csvContent.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Error saving CSV file: \(error.localizedDescription)")
            return nil
        }
    }
}
