import Foundation
import CoreLocation // Needed for CLLocationCoordinate2D

// Enum to represent the current recording status
enum RecordingState {
    case notRecording
    case recordingContinuous
    // Manual recording doesn't need a persistent state, it's event-driven
}

// Manages the app's data (Fields, Points) and recording state.
class DataManager: ObservableObject {

    // MARK: - Properties

    private let userDefaults: UserDefaults // Instance to use for persistence

    // MARK: - Published Properties (for UI updates)

    @Published var fields: [Field] = []
    @Published var selectedFieldID: UUID? {
         didSet {
             // Update the selectedField object when the ID changes
             selectedField = fields.first { $0.id == selectedFieldID }
             // Trigger area calculation for the new selection
             updateCalculatedAreaForSelectedField()
         }
     }

    @Published var recordingState: RecordingState = .notRecording
    @Published var currentFieldArea: Double? = nil // Stores the calculated area for the selected field
    // Published properties for loop closure feature
    @Published var isNearStartPoint: Bool = false
    @Published var canCloseLoop: Bool = false

    // Convenience computed property to get the actual selected field
    @Published private(set) var selectedField: Field? // Read-only access from outside

    // Private properties
    private var currentRecordingStartTime: Date? // Tracks start time for corner detection

    // Constants
    private let PROXIMITY_THRESHOLD: Double = 5.0 // Meters. Threshold for "near start point"
    private let MIN_POINTS_FOR_LOOP_CLOSURE: Int = 3 // Minimum points needed in session to allow closing loop

    // MARK: - Initialisation

    // Allow injecting a specific UserDefaults instance, defaulting to .standard
    init(userDefaults: UserDefaults = .standard) {
        self.userDefaults = userDefaults
        // Load existing data
        loadData()
        // Set initial selection if data exists
        if selectedFieldID == nil,
           let firstField = fields.first {
            selectedFieldID = firstField.id
        }
        // Ensure selectedField is initially set based on loaded selectedFieldID
        selectedField = fields.first { $0.id == selectedFieldID }
    }

    // MARK: - Data Management Methods

    func createField(name: String) -> Field {
        let newField = Field(name: name)
        fields.append(newField)
        // Update selection and published property
        selectedFieldID = newField.id
        // selectedField = newField // This is handled by didSet on selectedFieldID
        saveData() // Save after modification
        return newField // Return the created field
    }

    func addGeoPoint(coordinate: CLLocationCoordinate2D,
                     elevation: CLLocationDistance?,
                     timestamp: Date,
                     type: PointType,
                     note: String?,
                     heading: Double? = nil,
                     to fieldID: UUID) -> GeoPoint? {
        guard let fieldIndex = fields.firstIndex(where: { $0.id == fieldID }) else {
            print("Error: Could not find field ID \(fieldID)")
            return nil // Return nil if field not found
        }

        let newPoint = GeoPoint(coordinate: coordinate,
                                elevation: elevation,
                                timestamp: timestamp,
                                type: type,
                                note: note,
                                heading: heading)

        fields[fieldIndex].geoPoints.append(newPoint)

        // Update published property if this is the selected field
        // Note: This direct update might be redundant if selectedFieldID's didSet handles it.
        // Ensure consistency.
        if fieldID == selectedFieldID {
            selectedField = fields[fieldIndex]
        }

        print("Added \(type) point to field '\(fields[fieldIndex].name)'. Total points: \(fields[fieldIndex].geoPoints.count)")
        saveData()
        // Trigger area update if points were added to the currently selected field
        if fieldID == self.selectedFieldID {
            updateCalculatedAreaForSelectedField()
        }
        return newPoint // Return the created point
    }

    // MARK: - Update Methods

    func updateFieldName(fieldId: UUID, newName: String) {
        guard let fieldIndex = fields.firstIndex(where: { $0.id == fieldId }) else {
            print("Error: Could not find field with ID \(fieldId) to update name.")
            return
        }
        fields[fieldIndex].name = newName
        saveData() // Save changes
        print("Updated field \(fieldId) name to \(newName)")
    }

    /// Updates the properties of a specific GeoPoint.
    /// Finds the point across all fields.
    /// - Parameters:
    ///   - pointId: The UUID of the GeoPoint to update.
    ///   - newType: The new PointType, or nil to keep the existing type.
    ///   - newNote: The new note, or nil to keep the existing note. Pass empty string "" to clear the note.
    func updateGeoPoint(pointId: UUID, newType: PointType?, newNote: String?) {
        var foundAndUpdated = false
        for fieldIndex in fields.indices {
            if let pointIndex = fields[fieldIndex].geoPoints.firstIndex(where: { $0.id == pointId }) {

                var updated = false
                // Update type if provided
                if let type = newType, fields[fieldIndex].geoPoints[pointIndex].pointType != type {
                    fields[fieldIndex].geoPoints[pointIndex].pointType = type
                    print("Updated point \(pointId) type to \(type)")
                    updated = true
                }

                // Update note if provided (handle nil vs empty string explicitly)
                if let note = newNote, fields[fieldIndex].geoPoints[pointIndex].note != note {
                    fields[fieldIndex].geoPoints[pointIndex].note = note.isEmpty ? nil : note // Store nil if empty string provided
                    print("Updated point \(pointId) note.")
                    updated = true
                }

                if updated {
                    // If changes were made, trigger potential UI updates if it's the selected field
                    if fields[fieldIndex].id == selectedFieldID {
                         // Reassign to trigger @Published update for selectedField
                         selectedField = fields[fieldIndex]
                    }
                    // Also consider reassigning the whole fields array if direct mutation doesn't trigger list updates reliably
                    // self.fields = self.fields

                    saveData()
                    // Trigger area update if points were modified in the currently selected field
                    updateCalculatedAreaForSelectedField()
                }
                foundAndUpdated = true
                break // Point found and processed, exit outer loop
            }
        }

        if !foundAndUpdated {
            print("Error: Could not find point with ID \(pointId) to update.")
        }
    }

    // MARK: - Helper Methods

    /// Checks if a field contains only marked spots (no continuous points)
    /// - Parameter fieldId: The UUID of the field to check
    /// - Returns: True if the field only contains marked or boundary points (no continuous points)
    func fieldContainsOnlyMarkedSpots(fieldId: UUID) -> Bool {
        guard let field = fields.first(where: { $0.id == fieldId }) else {
            return false // Field not found
        }

        // If the field is empty, return false (not "only marked spots")
        if field.geoPoints.isEmpty {
            return false
        }

        // Check if there are any continuous points
        let hasContinuousPoints = field.geoPoints.contains(where: { $0.pointType == .continuous })

        // Return true if there are no continuous points (only marked or boundary points)
        return !hasContinuousPoints
    }

    /// Checks if a field contains any continuous points.
    /// - Parameter fieldId: The UUID of the field to check.
    /// - Returns: True if the field contains at least one point of type `.continuous`.
    func fieldContainsContinuousPoints(fieldId: UUID) -> Bool {
        guard let field = fields.first(where: { $0.id == fieldId }) else {
            return false // Field not found
        }
        // Check if there are any continuous points
        return field.geoPoints.contains(where: { $0.pointType == .continuous })
    }

    // MARK: - Delete Methods

    func deleteField(fieldId: UUID) -> Bool {
        guard let fieldIndex = fields.firstIndex(where: { $0.id == fieldId }) else {
            print("Error: Could not find field with ID \(fieldId) to delete.")
            return false
        }

        // Remove the field
        fields.remove(at: fieldIndex)

        // Update selection if the deleted field was selected
        if selectedFieldID == fieldId {
            // Select the first field if available, otherwise nil
            selectedFieldID = fields.first?.id
            // selectedField is updated by didSet
        }

        saveData() // Save changes
        print("Deleted field \(fieldId)")
        // Trigger area update (it will become nil if no field is selected)
        updateCalculatedAreaForSelectedField()
        return true
    }

    /// Deletes a specific GeoPoint by its ID from whichever field contains it.
    /// - Parameter pointId: The UUID of the GeoPoint to delete.
    /// - Returns: True if the point was found and deleted, false otherwise.
    func deleteGeoPoint(pointId: UUID) -> Bool {
        var foundAndDeleted = false
        for fieldIndex in fields.indices {
            // Check if the point exists in this field
            if let pointIndex = fields[fieldIndex].geoPoints.firstIndex(where: { $0.id == pointId }) {
                // Remove the point
                let removedPoint = fields[fieldIndex].geoPoints.remove(at: pointIndex)
                print("Deleted point \(removedPoint.id) (Type: \(removedPoint.pointType)) from field '\(fields[fieldIndex].name)'.")

                // If this was the selected field, update the published property to trigger UI refresh
                if fields[fieldIndex].id == selectedFieldID {
                    selectedField = fields[fieldIndex]
                    // Trigger area update after deletion
                    updateCalculatedAreaForSelectedField()
                }
                // Save data after modification
                saveData()
                foundAndDeleted = true
                break // Exit loop once point is found and deleted
            }
        }

        if !foundAndDeleted {
            print("Error: Could not find point with ID \(pointId) to delete.")
        }
        return foundAndDeleted
    }

    // MARK: - Recording State Management

    func startContinuousRecording() {
        guard selectedFieldID != nil else { // CORRECTED: Just check if not nil
            print("Error: Cannot start continuous recording without a selected field.")
            // TODO: Show alert to user
            return
        }
        recordingState = .recordingContinuous
        currentRecordingStartTime = Date() // Record the start time
        // Reset loop closure flags
        isNearStartPoint = false
        canCloseLoop = false
        print("Started continuous recording for field: \(selectedField?.name ?? "N/A") at \(currentRecordingStartTime!)")
    }

    func stopContinuousRecording() {
        guard recordingState == .recordingContinuous, let fieldID = selectedFieldID, let startTime = currentRecordingStartTime else {
            // If not recording, or no field selected, or start time missing, do nothing
            if recordingState != .recordingContinuous { print("Not in continuous recording state.") }
            else if selectedFieldID == nil { print("No field selected.") }
            else { print("Recording start time missing.") }
             // Reset flags even if stopping abnormally
            isNearStartPoint = false
            canCloseLoop = false
            return
        }

        print("Stopping continuous recording for field: \(selectedField?.name ?? "N/A"). Processing corners...")

        // Perform corner detection before stopping
        detectAndMarkCorners(for: fieldID, since: startTime)

        // --- Discard points after explicit loop closure ---
        var closingTimestamp: Date? = nil
        if let fieldIndex = fields.firstIndex(where: { $0.id == fieldID }), // Find field index again
           let lastPoint = fields[fieldIndex].geoPoints.last,               // Check the very last point added
           lastPoint.note == "Loop Closed",                                // Check if it's the auto-added closing point
           lastPoint.timestamp >= startTime                               // Check if it belongs to the current session
        {
            closingTimestamp = lastPoint.timestamp
            print("Discard Check: Loop was closed via button at \(closingTimestamp!).")

            // Find the original index for safety
            let originalCount = fields[fieldIndex].geoPoints.count
            fields[fieldIndex].geoPoints.removeAll { point in
                // Remove points from the *current session* that are *strictly after* the closing point
                point.timestamp > closingTimestamp! && point.timestamp >= startTime
            }
            let removedCount = originalCount - fields[fieldIndex].geoPoints.count
            if removedCount > 0 {
                print("Discard Check: Removed \(removedCount) points recorded after loop closure.")
                // Re-assign to trigger potential UI updates if it's the selected field
                 if fieldID == selectedFieldID {
                      selectedField = fields[fieldIndex]
                 }
            }
        }
        // --- End discard logic ---

        // Clear start time and set state to not recording
        currentRecordingStartTime = nil
        recordingState = .notRecording
        isNearStartPoint = false // Reset proximity flag
        canCloseLoop = false // Reset close loop flag
        print("Stopped continuous recording and corner processing completed.")

        // Trigger area calculation now that recording has stopped
        updateCalculatedAreaForSelectedField()
    }

    // MARK: - Loop Closure Logic

    /// Called by LocationManager whenever a new point is added during continuous recording.
    /// Checks proximity to the start point and updates published state.
    func updateProximityStatus(currentLocation: CLLocation) {
        guard recordingState == .recordingContinuous,
              let fieldID = selectedFieldID,
              let startTime = currentRecordingStartTime,
              let fieldIndex = fields.firstIndex(where: { $0.id == fieldID }) else {
            // Not recording, no selection, no start time, or field not found - reset flags
            if isNearStartPoint || canCloseLoop {
                 isNearStartPoint = false
                 canCloseLoop = false
                 print("Proximity Check: Resetting flags (not recording or missing data).")
            }
            return
        }

        // Find points added during this session
        let sessionPoints = fields[fieldIndex].geoPoints.filter {
            $0.timestamp >= startTime
        }.sorted { $0.timestamp < $1.timestamp }

        // Ensure minimum number of points exist before checking proximity
        guard sessionPoints.count >= MIN_POINTS_FOR_LOOP_CLOSURE else {
            if isNearStartPoint || canCloseLoop {
                isNearStartPoint = false
                canCloseLoop = false
                print("Proximity Check: Resetting flags (less than \(MIN_POINTS_FOR_LOOP_CLOSURE) points in session).")
            }
            return
        }

        // Get the first point of the session
        guard let startPoint = sessionPoints.first else {
            // Should not happen if count >= MIN_POINTS_FOR_LOOP_CLOSURE, but safeguard
             if isNearStartPoint || canCloseLoop {
                isNearStartPoint = false
                canCloseLoop = false
                print("Proximity Check: Resetting flags (could not find start point).")
             }
            return
        }

        // Calculate distance from current location to start point
        let startPointLocation = CLLocation(latitude: startPoint.latitude, longitude: startPoint.longitude)
        let distance = currentLocation.distance(from: startPointLocation)

        // Update published properties
        let currentlyNear = distance < PROXIMITY_THRESHOLD
        let canCurrentlyClose = currentlyNear && recordingState == .recordingContinuous // Redundant check? Keep for safety

        if currentlyNear != isNearStartPoint {
            isNearStartPoint = currentlyNear
            print("Proximity Check: User is \(isNearStartPoint ? "NEAR" : "FAR from") start point (Distance: \(String(format: "%.1f", distance))m).")
        }

        if canCurrentlyClose != canCloseLoop {
            canCloseLoop = canCurrentlyClose
            print("Proximity Check: Can close loop: \(canCloseLoop)")
        }
    }

    /// Closes the loop for the current recording session by adding the start point again.
    /// Then stops the recording session.
    func closeLoop() {
        guard recordingState == .recordingContinuous,
              let fieldID = selectedFieldID,
              let startTime = currentRecordingStartTime,
              let fieldIndex = fields.firstIndex(where: { $0.id == fieldID }) else {
            print("Close Loop Error: Cannot close loop (not recording or missing data).")
            return
        }

         // Find the start point of the session
        let startPoint = fields[fieldIndex].geoPoints.filter {
            $0.timestamp >= startTime
        }.sorted { $0.timestamp < $1.timestamp }.first

        guard let pointToAdd = startPoint else {
            print("Close Loop Error: Could not find start point to duplicate.")
            // Optionally stop recording anyway? Or just report error?
            stopContinuousRecording() // Stop recording even if start point missing
            return
        }

        print("Close Loop: Adding closing point (duplicate of start point ID: \(pointToAdd.id)).")

        // Add a new point with the start point's coordinates but current time and boundary type
        _ = addGeoPoint(
            coordinate: CLLocationCoordinate2D(latitude: pointToAdd.latitude, longitude: pointToAdd.longitude),
            elevation: pointToAdd.altitude,
            timestamp: Date(), // Use current time for the closing point
            type: .boundary, // Mark as boundary
            note: "Loop Closed", // Add a note indicating auto-closure
            to: fieldID
        )

        // Stop recording (which will also trigger corner detection etc.)
        // The addGeoPoint above already saved data, but stop will save again after corner detection.
        stopContinuousRecording()
    }

    // MARK: - Persistence

    private func saveData() {
        // Encode the fields array and save to the injected UserDefaults instance
        do {
             let encoder = JSONEncoder()
             encoder.outputFormatting = .prettyPrinted // Optional: for debugging
             let encoded = try encoder.encode(fields)
             userDefaults.set(encoded, forKey: "fieldData") // Use injected instance
             print("Data saved to UserDefaults domain: \(userDefaults.description)")
         } catch {
             print("Error encoding field data: \(error.localizedDescription)")
             // Consider logging the full error object: print(error)
         }
    }

    private func loadData() {
        print("Attempting to load data from UserDefaults domain: \(userDefaults.description)")
        // Load and decode data from the injected UserDefaults instance
        guard let savedData = userDefaults.data(forKey: "fieldData") else {
            print("No saved data found in domain: \(userDefaults.description)")
            fields = []
            selectedFieldID = nil
            selectedField = nil
            return
        }

        do {
            let decoder = JSONDecoder()
            let decodedFields = try decoder.decode([Field].self, from: savedData)
            fields = decodedFields
            print("Data loaded. Found \(fields.count) fields.")
            // Restore selection if possible - select first field if no ID saved or saved ID invalid
            let previouslySelectedID = selectedFieldID // Assuming this might be loaded separately or persisted elsewhere?
                                                        // If not, the logic might need adjustment. For now, assume it might exist.

            if let validID = previouslySelectedID, fields.contains(where: { $0.id == validID }) {
                selectedFieldID = validID
            } else {
                selectedFieldID = fields.first?.id // Fallback to first field
            }
            // selectedField = fields.first { $0.id == selectedFieldID } // Updated by didSet
            return
        } catch {
            print("Error decoding field data: \(error.localizedDescription)")
            // Consider logging the full error object: print(error)
            // Decide if corrupted data should be cleared or handled differently
            fields = [] // Ensure fields is empty if loading failed
            selectedFieldID = nil
            selectedField = nil
        }
    }

    // MARK: - Area Calculation Trigger

    /// Triggers the asynchronous calculation of the area for the currently selected field
    /// and updates the published `currentFieldArea` property.
    /// Ensures calculation doesn't run during active recording.
    private func updateCalculatedAreaForSelectedField() {
        // Use Task to perform this asynchronously
        Task {
            // Prevent calculation during active recording
            guard await MainActor.run(body: { self.recordingState }) == .notRecording else {
                NSLog("DATAMANAGER_DEBUG: Skipping area update trigger because recording is active.")
                // Optionally clear the area if recording starts?
                // await MainActor.run { self.currentFieldArea = nil }
                return
            }

            // Capture the selected ID safely
            guard let fieldId = await MainActor.run(body: { self.selectedFieldID }) else {
                // No field selected, ensure area is nil
                await MainActor.run { self.currentFieldArea = nil }
                return
            }

            NSLog("DATAMANAGER_DEBUG: Triggering async area calculation for field ID \(fieldId)...")
            let calculatedArea = await self.calculateFieldArea(fieldId: fieldId) // calculateFieldArea is already async

            // Update the published property on the main thread
            await MainActor.run { // Ensure UI update is on main thread
                // Check if the selected field is still the same one we calculated for,
                // in case it changed rapidly during the calculation.
                if self.selectedFieldID == fieldId {
                    NSLog("DATAMANAGER_DEBUG: Async area calculation complete. Updating currentFieldArea. Result: \(calculatedArea?.description ?? "nil")")
                    self.currentFieldArea = calculatedArea
                } else {
                    NSLog("DATAMANAGER_DEBUG: Async area calculation complete, but selected field changed during calculation. Discarding result.")
                    // Optionally trigger a new calculation for the *new* selected field here, or let its didSet handle it.
                }
            }
        }
    }

    // MARK: - Utility Methods

    // Removed the placeholder UTMCoordinate struct
    // Removed the placeholder convertToUTM function
    // Removed the placeholder calculatePlanarArea function

    /// Calculates the total distance traveled in a field based on its GPS points
    /// Returns distance in meters
    func calculateTotalDistance(fieldId: UUID) -> Double {
        guard let field = getField(fieldId: fieldId),
              field.geoPoints.count >= 2 else {
            return 0 // Need at least 2 points to calculate distance
        }

        // Sort points by timestamp to ensure we're calculating distance in chronological order
        let sortedPoints = field.geoPoints.sorted { $0.timestamp < $1.timestamp }

        var totalDistance: Double = 0

        // Calculate distance between consecutive points
        for i in 0..<(sortedPoints.count - 1) {
            // Use Geodesy library for potentially more accurate distance if needed,
            // but CLLocation distance is usually sufficient for this.
            let p1 = CLLocation(latitude: sortedPoints[i].latitude, longitude: sortedPoints[i].longitude)
            let p2 = CLLocation(latitude: sortedPoints[i+1].latitude, longitude: sortedPoints[i+1].longitude)

            totalDistance += p1.distance(from: p2) // Distance in meters
        }

        return totalDistance
    }

    /// Calculates the approximate area of a field based on its GPS points using UTM projection via the UTMConversion library.
    /// Assumes all points (if >= 3) define the boundary unless otherwise specified by future logic enhancements.
    /// Returns area in square meters, or nil if area cannot be calculated or UTM conversion fails.
    /// Runs asynchronously on a background thread.
    func calculateFieldArea(fieldId: UUID) async -> Double? {
        // Find the field
        // Access fields on the main actor if DataManager is a MainActor
        // Otherwise, ensure thread-safe access if needed.
        guard let field = await MainActor.run(body: { self.getField(fieldId: fieldId) }) else {
            print("Error: Field not found for area calculation.")
            return nil
        }

        // Use all points if there are at least 3
        let pointsToUse = await MainActor.run { field.geoPoints } // Access points safely
        guard pointsToUse.count >= 3 else {
            print("Cannot calculate area for field \(fieldId), requires at least 3 points.")
            return nil
        }
        print("Using all \(pointsToUse.count) points for area calculation for field \(fieldId). Starting async calculation.")

        // Perform potentially blocking calculations outside the MainActor

        // 1. Calculate Centroid
        var avgLat: Double = 0
        var avgLon: Double = 0
        for point in pointsToUse {
            avgLat += point.latitude
            avgLon += point.longitude
        }
        avgLat /= Double(pointsToUse.count)
        avgLon /= Double(pointsToUse.count)

        // 2. Sort points by angle around centroid
        let sortedGeoPoints = pointsToUse.sorted { p1, p2 in
            let angle1 = atan2(p1.latitude - avgLat, p1.longitude - avgLon)
            let angle2 = atan2(p2.latitude - avgLat, p2.longitude - avgLon)
            return angle1 < angle2
        }

        // 3. Convert sorted Lat/Lon points to UTM
        var utmCoordinates: [LocalUTMCoordinate] = []
        var commonZone: UInt? = nil
        var commonHemisphere: String? = nil

        for geoPoint in sortedGeoPoints {
            let clLocationCoord = CLLocationCoordinate2D(latitude: geoPoint.latitude, longitude: geoPoint.longitude)
            let utm = LocalUTMCoordinate(from: clLocationCoord.toUTMCoordinate())

            if commonZone == nil {
                commonZone = utm.zone
                commonHemisphere = utm.hemisphere
            } else if utm.zone != commonZone || utm.hemisphere != commonHemisphere {
                print("Error: Points span multiple UTM zones/hemispheres. Planar area calculation is invalid. Aborting.")
                return nil
            }
            utmCoordinates.append(utm)
        }

        // 4. Calculate area using planar Shoelace formula
        guard utmCoordinates.count >= 3 else { return nil } // Should be redundant, but safe check

        var planarArea: Double = 0.0
        for i in 0..<utmCoordinates.count {
            let p1 = utmCoordinates[i]
            let p2 = utmCoordinates[(i + 1) % utmCoordinates.count] // Wrap around
            planarArea += (p1.easting * p2.northing - p2.easting * p1.northing)
        }

        let finalArea = abs(planarArea) / 2.0 // Area in square meters

        print("Async calculation finished for field \(fieldId): \(finalArea) sq meters")
        return finalArea
    }

    /// Helper function to get a specific field by ID
    private func getField(fieldId: UUID) -> Field? {
        return fields.first { $0.id == fieldId }
    }

    // MARK: - Corner Detection

    private let CORNER_ANGLE_THRESHOLD: Double = 30.0 // Degrees. Tune this value.
    private let MIN_DISTANCE_BETWEEN_POINTS_FOR_CORNER: Double = 1.0 // Meters. Prevents noise from stationary points.

    /// Detects corners in the most recent continuous recording session and updates point types.
    private func detectAndMarkCorners(for fieldID: UUID, since startTime: Date) {
        guard let fieldIndex = fields.firstIndex(where: { $0.id == fieldID }) else {
            print("Corner Detection Error: Field ID \(fieldID) not found.")
            return
        }

        // 1. Filter relevant points: .continuous type recorded since startTime, sorted by time
        let recentContinuousPoints = fields[fieldIndex].geoPoints.filter {
            $0.pointType == .continuous && $0.timestamp >= startTime
        }.sorted { $0.timestamp < $1.timestamp }

        guard recentContinuousPoints.count >= 3 else {
            print("Corner Detection: Not enough points (\(recentContinuousPoints.count)) in the session to detect corners.")
            return // Need at least 3 points to calculate an angle
        }

        print("Corner Detection: Processing \(recentContinuousPoints.count) points for field \(fieldID).")
        var cornersDetected = 0

        // 2. Iterate through triplets of points (p1, p2, p3)
        for i in 0..<(recentContinuousPoints.count - 2) {
            let p1 = recentContinuousPoints[i]
            let p2 = recentContinuousPoints[i+1]
            let p3 = recentContinuousPoints[i+2]

            // Calculate distances to avoid noise from very close points
            let dist12 = calculateDistanceBetween(p1, p2)
            let dist23 = calculateDistanceBetween(p2, p3)

            // Skip if points are too close together
            guard dist12 > MIN_DISTANCE_BETWEEN_POINTS_FOR_CORNER && dist23 > MIN_DISTANCE_BETWEEN_POINTS_FOR_CORNER else {
                continue
            }

            // 3. Calculate bearings
            let bearing12 = calculateBearing(from: p1, to: p2)
            let bearing23 = calculateBearing(from: p2, to: p3)

            // 4. Calculate angle change (handle wrap-around 360 degrees)
            var angleChange = bearing23 - bearing12
            if angleChange > 180.0 {
                angleChange -= 360.0
            } else if angleChange <= -180.0 {
                angleChange += 360.0
            }

            // 5. Check if angle change exceeds threshold
            if abs(angleChange) > CORNER_ANGLE_THRESHOLD {
                // Corner detected at p2
                // Find the original index of p2 in the main geoPoints array
                if let originalPointIndex = fields[fieldIndex].geoPoints.firstIndex(where: { $0.id == p2.id }) {
                    // Update the point type only if it's still continuous (might have been marked manually)
                    if fields[fieldIndex].geoPoints[originalPointIndex].pointType == .continuous {
                        fields[fieldIndex].geoPoints[originalPointIndex].pointType = .boundary
                        cornersDetected += 1
                        print("Corner detected at point index \(originalPointIndex) (ID: \(p2.id)). Angle change: \(String(format: "%.1f", angleChange))°")
                    }
                }
            }
        }

        if cornersDetected > 0 {
            print("Corner Detection: Marked \(cornersDetected) points as boundaries for field \(fieldID).")
            // Update the selectedField if it's the one being modified, to trigger UI updates
            if fieldID == selectedFieldID {
                 selectedField = fields[fieldIndex]
            }
            saveData() // Save changes after processing all points
        }
    }

    /// Calculates the initial bearing (forward azimuth) from one GeoPoint to another.
    /// Uses Haversine formula principles.
    /// - Returns: Bearing in degrees (0-360).
    private func calculateBearing(from startPoint: GeoPoint, to endPoint: GeoPoint) -> Double {
        let lat1 = startPoint.latitude * .pi / 180.0
        let lon1 = startPoint.longitude * .pi / 180.0
        let lat2 = endPoint.latitude * .pi / 180.0
        let lon2 = endPoint.longitude * .pi / 180.0

        let dLon = lon2 - lon1

        let y = sin(dLon) * cos(lat2)
        let x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dLon)
        let bearingRad = atan2(y, x)

        // Convert bearing from radians to degrees and normalise to 0-360 range
        var bearingDeg = bearingRad * 180.0 / .pi
        bearingDeg = (bearingDeg + 360.0).truncatingRemainder(dividingBy: 360.0)

        return bearingDeg
    }

    /// Calculates distance between two GeoPoints using CLLocation.
    private func calculateDistanceBetween(_ p1: GeoPoint, _ p2: GeoPoint) -> Double {
        let loc1 = CLLocation(latitude: p1.latitude, longitude: p1.longitude)
        let loc2 = CLLocation(latitude: p2.latitude, longitude: p2.longitude)
        return loc1.distance(from: loc2) // Distance in meters
    }

    // Add other methods or properties as needed...
}
