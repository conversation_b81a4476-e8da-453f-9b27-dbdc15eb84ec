# Overview

This document provides a technical overview of the CropCompass GPS application's structure and core components.

## Core Components

*   **`LocationManager.swift`**: Manages all interactions with the Core Location framework, including requesting permissions, configuring accuracy and activity types, receiving location and heading updates, and handling background location capabilities.
*   **`DataManager.swift`**: Responsible for managing the application's data model, including fields, geographical points (GeoPoint), and spot points. It handles data persistence (saving/loading to UserDefaults), calculations (area, distance), and managing the recording state.
*   **Views (`/Views`)**: Contains the SwiftUI views that make up the user interface, such as `MapView`, `RecordingView`, `SettingsView`, `CompassView`, etc.
*   **Models (`/Models`, `DataModels.swift`)**: Defines the data structures used throughout the app, like `Field`, `GeoPoint`, `SpotPoint`, and various enums for settings (`MapStyleChoice`, `MeasurementSystem`).
*   **Exporters (`/Exporters`)**: Contains logic for converting the application's data into standard file formats like GPX, KML, and CSV.
*   **Utilities (`/Utilities`)**: Holds helper functions and structures, such as `UnitConverter`.
*   **`CropCompass_GPSApp.swift`**: The main entry point for the SwiftUI application lifecycle.
*   **`Info.plist`**: Configuration file containing necessary permissions descriptions and background mode declarations.

## Data Flow

1.  **Location Updates**: `LocationManager` receives raw `CLLocation` updates from Core Location.
2.  **Data Saving**: If `DataManager` is in a recording state (`.recordingContinuous`), `LocationManager` passes the `CLLocation` data to `DataManager`.
3.  **Point Creation**: `DataManager` converts the `CLLocation` into a `GeoPoint` object (containing coordinate, elevation, timestamp, etc.) and adds it to the currently selected `Field`.
4.  **Persistence**: `DataManager` periodically (or on significant events) saves the array of `Field` objects (including all their points) to `UserDefaults` using JSON encoding.
5.  **UI Updates**: `LocationManager` and `DataManager` are `ObservableObject`s. Changes to their `@Published` properties (e.g., current location, heading, fields list, selected field, recording state) automatically trigger updates in the relevant SwiftUI views.
6.  **Settings**: `SettingsView` reads and writes values directly to `@AppStorage` for persistent UI preferences (map style, units) and interacts with `LocationManager` to update GPS parameters (distance filter, activity type) and manage permissions.

## Key Interactions

*   **Permissions**: `LocationManager` handles the requesting of "When In Use" and "Always" location permissions, reacting to user choices via the `locationManagerDidChangeAuthorization` delegate method.
*   **Background Updates**: `LocationManager` enables `allowsBackgroundLocationUpdates` only when "Always" authorisation is granted, ensuring compliance with system requirements.
*   **Recording State**: The UI interacts with `DataManager` to start/stop continuous recording or mark spot points. This state change dictates whether incoming location data from `LocationManager` is saved. 