# DataManager Documentation

## Role

The `DataManager` class is the central hub for managing application data. It handles the creation, modification, deletion, and persistence of `Field` objects and their associated `GeoPoint` and `SpotPoint` data. It also manages the application's recording state and provides calculation utilities.

## Key Properties

*   `@Published fields: [Field]`: An array containing all the field data managed by the application.
*   `@Published selectedFieldID: UUID?`: The ID of the currently selected field. Changes trigger updates to `selectedField`.
*   `@Published selectedField: Field?`: The `Field` object corresponding to `selectedFieldID`. Automatically updated when `selectedFieldID` changes.
*   `@Published recordingState: RecordingState`: An enum (`.notRecording`, `.recordingContinuous`, `.markingSpot`) indicating the current recording status.
*   `userDefaults`: An instance of `UserDefaults` (potentially injected for testability) used for data persistence.

## Data Models (Assumed based on context)

*   **`Field`**: Represents an agricultural field. Contains properties like `id` (UUID), `name` (String), `creationDate` (Date), and arrays of `geoPoints` and `spotPoints`.
*   **`GeoPoint`**: Represents a single GPS coordinate recorded as part of a boundary or track. Contains `latitude`, `longitude`, `elevation`, `timestamp`, and a `type` (e.g., `.boundary`, `.continuous`, `.spot`).
*   **`SpotPoint`**: Similar to `GeoPoint` but potentially with additional properties like a `note` (String).

## Core Methods

*   **Field Management:**
    *   `addField(name: String)`: Creates a new `Field` object with the given name, assigns a unique UUID, adds it to the `fields` array, selects it, and saves the data.
    *   `deleteField(fieldId: UUID)`: Removes the specified field from the `fields` array and saves the changes.
    *   `updateFieldName(fieldId: UUID, newName: String)`: Updates the name of the specified field and saves the changes.
    *   `selectField(fieldId: UUID?)`: Updates `selectedFieldID` (and consequently `selectedField`).
*   **Point Management:**
    *   `addGeoPoint(coordinate: CLLocationCoordinate2D, elevation: CLLocationDistance, timestamp: Date, type: GeoPointType, note: String?, heading: Double?, to fieldId: UUID)`: Creates a `GeoPoint` and adds it to the specified field. Returns the created point (or potentially a Bool indicating success).
    *   `addSpotPoint(...)`: (Assumed) Similar method specifically for creating `SpotPoint` objects, likely including a note.
    *   `deleteGeoPoint(...)`, `updateGeoPointNote(...)`: (Assumed) Methods for managing individual points within a field.
*   **Recording State:**
    *   `startContinuousRecording()`: Sets `recordingState` to `.recordingContinuous` if a field is selected.
    *   `stopContinuousRecording()`: Sets `recordingState` to `.notRecording`.
    *   `startMarkingSpot()` / `finishMarkingSpot(...)`: (Assumed) Methods to manage the state for recording a single spot point, possibly transitioning through `.markingSpot`.
*   **Calculations:**
    *   `calculateTotalDistance(fieldId: UUID) -> Double`: Calculates the path distance in metres by summing the distances between chronologically sorted `GeoPoint`s in the specified field.
    *   `calculateFieldArea(fieldId: UUID) -> Double?`: Calculates the approximate planar area in square metres using the Shoelace formula on UTM-converted coordinates of the field's points (sorted angularly around the centroid). Returns `nil` if calculation is not possible (e.g., < 3 points, points span UTM zones).
*   **Persistence:**
    *   `saveData()`: Encodes the `fields` array to JSON and saves it to `UserDefaults` under the key "fieldData".
    *   `loadData()`: Loads JSON data from `UserDefaults`, decodes it into the `fields` array, and attempts to restore the `selectedFieldID`.

## Persistence Mechanism

*   The entire `fields` array, including all nested `GeoPoint` and `SpotPoint` objects, is serialised to JSON using `JSONEncoder`.
*   The resulting JSON data is stored in `UserDefaults`.
*   This approach is simple but may become inefficient or hit `UserDefaults` size limits if very large amounts of data are stored. For more robust storage, Core Data or Realm might be considered in future iterations. 