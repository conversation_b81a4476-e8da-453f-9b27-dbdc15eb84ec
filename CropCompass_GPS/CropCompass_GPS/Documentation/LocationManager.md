# LocationManager Documentation

## Role

The `LocationManager` class serves as the primary interface with Apple's Core Location framework. It encapsulates the setup, configuration, and handling of location and heading updates, as well as managing user permissions for location access.

## Key Properties

*   `manager`: The private `CLLocationManager` instance that directly interacts with the system services.
*   `dataManager`: A weak reference to the `DataManager` used to pass location data for recording.
*   `@Published location: CLLocation?`: The most recently received valid location update.
*   `@Published authorisationStatus: CLAuthorizationStatus`: The current authorisation status granted by the user (e.g., `.authorizedWhenInUse`, `.denied`).
*   `@Published accuracyAuthorisation: CLAccuracyAuthorization`: Indicates whether the app has full or reduced location accuracy.
*   `@Published isUpdatingLocation: Bool`: Tracks whether the manager is actively requesting location updates from the system.
*   `@Published heading: CLHeading?`: The most recent compass heading update.
*   `@Published headingAccuracy: Double`: The accuracy of the compass heading in degrees.
*   `@Published isUpdatingHeading: Bool`: Tracks whether the manager is actively requesting heading updates.
*   `@Published distanceFilter: Double`: The minimum distance (in metres) the device must move horizontally before a location update event is generated. Configured via `SettingsView`.
*   `@Published activityType: CLActivityType`: The type of activity being performed, used by Core Location to optimise power usage and filtering (e.g., `.fitness`, `.automotiveNavigation`). Configured via `SettingsView`.
*   `@Published signalStrength: GPSSignalStrength`: An enum value representing the estimated GPS signal quality based on horizontal accuracy.

## Initialisation

*   Takes a `DataManager` instance during initialisation to establish communication for saving points.
*   Sets itself as the delegate for the internal `CLLocationManager`.
*   Configures initial `desiredAccuracy`, `activityType`, and `distanceFilter`.
*   Checks if running in a SwiftUI Preview environment to disable certain features like background updates and authorisation requests.
*   Calls `requestAuthorisation()` to initiate the permission flow if needed.
*   Calls `updateBackgroundCapabilities()` to set initial background mode based on current authorisation.

## Core Methods

*   `requestAuthorisation()`:
    *   Checks the current `authorizationStatus`.
    *   If `.notDetermined`, calls `manager.requestWhenInUseAuthorization()`.
    *   If `.authorizedWhenInUse`, calls `manager.requestAlwaysAuthorization()` to request background access.
    *   If already `.authorizedAlways`, `.denied`, or `.restricted`, it does nothing.
*   `startUpdatingLocation()`:
    *   Checks if authorised (`.authorizedWhenInUse` or `.authorizedAlways`) by querying `manager.authorizationStatus` directly.
    *   If authorised and not already updating, calls `manager.startUpdatingLocation()` and sets `isUpdatingLocation` to `true`.
    *   Does not start updates if not authorised or if running in a preview.
*   `stopUpdatingLocation()`:
    *   Calls `manager.stopUpdatingLocation()` if `isUpdatingLocation` is `true`.
    *   Sets `isUpdatingLocation` to `false`.
*   `startUpdatingHeading()` / `stopUpdatingHeading()`: Start/stop compass heading updates if available on the device.
*   `updateSignalStrength()`: Calculates a `GPSSignalStrength` enum value based on the `horizontalAccuracy` of the current `location`.
*   `updateDistanceFilter(_:)`: Updates the `distanceFilter` property on both the `LocationManager` and the internal `CLLocationManager` instance.
*   `updateActivityType(_:)`: Updates the `activityType` property on both the `LocationManager` and the internal `CLLocationManager` instance.

## Delegate Methods (`CLLocationManagerDelegate`)

*   `locationManagerDidChangeAuthorization(_:)`:
    *   Triggered when the user grants/denies permission or when the manager is initialised.
    *   Updates the `@Published authorisationStatus` and `accuracyAuthorisation` properties.
    *   Calls `updateBackgroundCapabilities(for:)` to enable/disable background features based on the new status.
    *   Handles the authorisation flow: requests "Always" if "When In Use" is granted, starts location updates if appropriate authorisation exists, requests temporary full accuracy if needed.
    *   Stops updates if permission is denied or restricted.
*   `locationManager(_:didUpdateLocations:)`:
    *   Triggered when new location data is received.
    *   Validates the location (checks accuracy, age).
    *   Updates the `@Published location` property.
    *   Calls `updateSignalStrength()`.
    *   If `DataManager` is in `.recordingContinuous` state and has a selected field, passes the location data to `DataManager`'s `addGeoPoint` method.
*   `locationManager(_:didFailWithError:)`: Handles errors from Core Location, logging them and potentially updating UI state (e.g., setting signal strength to none on `.locationUnknown`).
*   `locationManager(_:didUpdateHeading:)`: Updates the `@Published heading` and `headingAccuracy` properties.

## Helper Methods

*   `updateBackgroundCapabilities(for:)`: Private method that enables/disables `manager.allowsBackgroundLocationUpdates`, `manager.pausesLocationUpdatesAutomatically`, and `manager.showsBackgroundLocationIndicator` based on the provided authorisation status and whether the app is running in a preview. 