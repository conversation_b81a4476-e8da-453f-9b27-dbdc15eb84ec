//
//  CropCompass_GPSApp.swift
//  CropCompass_GPS
//
//  Created by <PERSON><PERSON> on 28/3/2025.
//

import SwiftUI
import UserNotifications

@main
struct CropCompass_GPSApp: App {
    // Add environment variable to monitor scene phase
    @Environment(\.scenePhase) var scenePhase

    // DataManager and LocationManager instances
    @StateObject private var dataManager = DataManager()
    // Use direct initialization based on standard UserDefaults for simplicity now
    @StateObject private var locationManager: LocationManager

    init() {
        // Request notification permissions when the app starts
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if granted {
                print("Notification permission granted")
            } else if let error = error {
                print("Notification permission error: \(error.localizedDescription)")
            }
        }

        // Initialize LocationManager within init, passing the DataManager instance
        // We need to do this here so dataManager is available when locationManager is created
        let dm = DataManager()
        _dataManager = StateObject(wrappedValue: dm)
        _locationManager = StateObject(wrappedValue: LocationManager(dataManager: dm))
    }

    var body: some Scene {
        WindowGroup {
            // Pass the managers to the ContentView.
            ContentView()
                .environmentObject(locationManager)
                .environmentObject(dataManager)
        }
        // Add the onChange modifier to monitor scene phase changes
        .onChange(of: scenePhase) { _, newPhase in
            switch newPhase {
            case .active:
                NSLog("APP_DEBUG: ScenePhase: App became active.")
                // Optional: Could potentially restart updates IF needed, but current logic
                // relies on manual start via recording button, which matches the requirement.
            case .inactive:
                NSLog("APP_DEBUG: ScenePhase: App became inactive.")
                // Stop location updates if the app becomes inactive AND we are NOT recording.
                if dataManager.recordingState != .recordingContinuous {
                    NSLog("APP_DEBUG: ScenePhase: App inactive and NOT recording. Stopping location updates.")
                    locationManager.stopUpdatingLocation()
                }
            case .background:
                NSLog("APP_DEBUG: ScenePhase: App entered background.")
                // Stop location updates if the app enters background AND we are NOT recording.
                if dataManager.recordingState != .recordingContinuous {
                    NSLog("APP_DEBUG: ScenePhase: App backgrounded and NOT recording. Stopping location updates.")
                    locationManager.stopUpdatingLocation()
                }
            @unknown default:
                NSLog("APP_DEBUG: ScenePhase: Unknown scene phase.")
            }
        }
    }
}
