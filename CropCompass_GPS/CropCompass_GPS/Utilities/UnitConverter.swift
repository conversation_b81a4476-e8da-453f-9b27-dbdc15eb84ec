import Foundation

/// Utility class for converting between different measurement units
struct UnitConverter {

    // MARK: - Distance Conversions

    /// Convert meters to the appropriate unit based on the measurement system
    /// - Parameters:
    ///   - meters: Distance in meters
    ///   - system: The measurement system to use
    /// - Returns: A formatted string with the appropriate unit
    static func formatDistance(_ meters: Double, system: MeasurementSystem) -> String {
        switch system {
        case .metric:
            if meters >= 1000 {
                let kilometers = meters / 1000
                return String(format: "%.2f km", kilometers)
            } else {
                return String(format: "%.1f m", meters)
            }
        case .imperial:
            let feet = meters * 3.28084
            if feet >= 5280 {
                let miles = feet / 5280
                return String(format: "%.2f mi", miles)
            } else {
                return String(format: "%.1f ft", feet)
            }
        }
    }

    /// Convert meters to the appropriate unit for display in the recording interval picker
    /// - Parameters:
    ///   - meters: Distance in meters
    ///   - system: The measurement system to use
    /// - Returns: A formatted string with the appropriate unit
    static func formatDistanceForPicker(_ meters: Double, system: MeasurementSystem) -> String {
        switch system {
        case .metric:
            return String(format: "%.1f m", meters)
        case .imperial:
            // For imperial values, we're directly using feet values in the options array
            // so we don't need to convert from meters
            if system == .imperial && [1.0, 3.0, 5.0, 10.0, 20.0].contains(meters) {
                // These are our predefined imperial values, so display them directly
                return String(format: "%.0f ft", meters) // No decimal places for cleaner display
            } else {
                // For any other values (like when converting from metric)
                let feet = meters * 3.28084
                return String(format: "%.1f ft", feet)
            }
        }
    }

    /// Convert a distance value from one system to another
    /// - Parameters:
    ///   - value: The distance value
    ///   - fromSystem: The source measurement system
    ///   - toSystem: The target measurement system
    /// - Returns: The converted distance value
    static func convertDistance(_ value: Double, from fromSystem: MeasurementSystem, to toSystem: MeasurementSystem) -> Double {
        switch (fromSystem, toSystem) {
        case (.metric, .imperial):
            return value * 3.28084 // meters to feet
        case (.imperial, .metric):
            return value / 3.28084 // feet to meters
        case (.metric, .metric), (.imperial, .imperial):
            return value // No conversion needed
        }
    }

    // MARK: - Area Conversions

    /// Convert square meters to the appropriate unit based on the measurement system
    /// - Parameters:
    ///   - squareMeters: Area in square meters
    ///   - system: The measurement system to use
    /// - Returns: A formatted string with the appropriate unit
    static func formatArea(_ squareMeters: Double, system: MeasurementSystem) -> String {
        switch system {
        case .metric:
            if squareMeters >= 10000 {
                let hectares = squareMeters / 10000
                return String(format: "%.2f ha", hectares)
            } else {
                return String(format: "%.1f m²", squareMeters)
            }
        case .imperial:
            let squareFeet = squareMeters * 10.7639
            if squareFeet >= 43560 {
                let acres = squareFeet / 43560
                return String(format: "%.2f ac", acres)
            } else {
                return String(format: "%.1f ft²", squareFeet)
            }
        }
    }

    /// Convert an area value from one system to another
    /// - Parameters:
    ///   - value: The area value
    ///   - fromSystem: The source measurement system
    ///   - toSystem: The target measurement system
    /// - Returns: The converted area value
    static func convertArea(_ value: Double, from fromSystem: MeasurementSystem, to toSystem: MeasurementSystem) -> Double {
        switch (fromSystem, toSystem) {
        case (.metric, .imperial):
            return value * 10.7639 // square meters to square feet
        case (.imperial, .metric):
            return value / 10.7639 // square feet to square meters
        case (.metric, .metric), (.imperial, .imperial):
            return value // No conversion needed
        }
    }

    // MARK: - Distance Filter Conversions

    /// Convert a distance filter value to meters regardless of the measurement system
    /// - Parameters:
    ///   - value: The distance filter value
    ///   - system: The measurement system the value is in
    /// - Returns: The distance filter value in meters
    static func distanceFilterToMeters(_ value: Double, from system: MeasurementSystem) -> Double {
        switch system {
        case .metric:
            return value // Already in meters
        case .imperial:
            // These are our predefined imperial values in feet, convert them to meters
            return value / 3.28084 // Convert feet to meters
        }
    }

    /// Get the appropriate distance filter options based on the measurement system
    /// - Parameter system: The measurement system to use
    /// - Returns: An array of distance filter options
    static func distanceFilterOptions(for system: MeasurementSystem) -> [Double] {
        switch system {
        case .metric:
            return [1.0, 2.0, 5.0, 10.0, 20.0] // Meters
        case .imperial:
            // Use conventional imperial values that are familiar to imperial system users
            return [1.0, 3.0, 5.0, 10.0, 20.0] // Feet
        }
    }
}
