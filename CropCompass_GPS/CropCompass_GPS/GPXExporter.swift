import Foundation

struct GPXExporter {
    
    // Export a single field to GPX format
    static func exportField(_ field: Field) -> String {
        let header = """
        <?xml version="1.0" encoding="UTF-8"?>
        <gpx version="1.1" 
             creator="CropCompass GPS"
             xmlns="http://www.topografix.com/GPX/1/1"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://www.topografix.com/GPX/1/1 
             http://www.topografix.com/GPX/1/1/gpx.xsd">
        <metadata>
            <name>Field: \(field.name)</name>
            <time>\(ISO8601DateFormatter().string(from: Date()))</time>
        </metadata>
        
        """
        
        let trackStart = """
        <trk>
            <name>\(field.name)</name>
            <trkseg>
        
        """
        
        var trackPoints = ""
        for point in field.geoPoints {
            let timeString = ISO8601DateFormatter().string(from: point.timestamp)
            var pointXML = """
                <trkpt lat="\(point.latitude)" lon="\(point.longitude)">
            """
            
            if let elevation = point.altitude {
                pointXML += """
                    <ele>\(elevation)</ele>
                """
            }
            
            pointXML += """
                    <time>\(timeString)</time>
            """
            
            if let note = point.note, !note.isEmpty {
                pointXML += """
                    <cmt>\(note)</cmt>
                """
            }
            
            pointXML += """
                </trkpt>
            """
            
            trackPoints += pointXML
        }
        
        let trackEnd = """
            </trkseg>
        </trk>
        """
        
        let footer = """
        </gpx>
        """
        
        return header + trackStart + trackPoints + trackEnd + footer
    }
    
    // Export multiple fields
    static func exportFields(_ fields: [Field]) -> String {
        let header = """
        <?xml version="1.0" encoding="UTF-8"?>
        <gpx version="1.1" 
             creator="CropCompass GPS"
             xmlns="http://www.topografix.com/GPX/1/1"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://www.topografix.com/GPX/1/1 
             http://www.topografix.com/GPX/1/1/gpx.xsd">
        <metadata>
            <name>CropCompass GPS Export</name>
            <time>\(ISO8601DateFormatter().string(from: Date()))</time>
        </metadata>
        
        """
        
        var tracks = ""
        for field in fields {
            let trackStart = """
            <trk>
                <name>\(field.name)</name>
                <trkseg>
            
            """
            
            var trackPoints = ""
            for point in field.geoPoints {
                let timeString = ISO8601DateFormatter().string(from: point.timestamp)
                var pointXML = """
                    <trkpt lat="\(point.latitude)" lon="\(point.longitude)">
                """
                
                if let elevation = point.altitude {
                    pointXML += """
                        <ele>\(elevation)</ele>
                    """
                }
                
                pointXML += """
                        <time>\(timeString)</time>
                """
                
                if let note = point.note, !note.isEmpty {
                    pointXML += """
                        <cmt>\(note)</cmt>
                    """
                }
                
                pointXML += """
                    </trkpt>
                """
                
                trackPoints += pointXML
            }
            
            let trackEnd = """
                </trkseg>
            </trk>
            """
            
            tracks += trackStart + trackPoints + trackEnd
        }
        
        let footer = """
        </gpx>
        """
        
        return header + tracks + footer
    }
    
    // Save GPX data to a temporary file and return the URL
    static func saveGPXToFile(gpxContent: String, fileName: String) -> URL? {
        let tempDirectoryURL = FileManager.default.temporaryDirectory
        let fileURL = tempDirectoryURL.appendingPathComponent(fileName)
        
        do {
            try gpxContent.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Error saving GPX file: \(error.localizedDescription)")
            return nil
        }
    }
}
