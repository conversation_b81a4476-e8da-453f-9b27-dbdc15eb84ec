import Foundation
import CoreLocation
import SwiftUI

/// A mock implementation of LocationManager for use in SwiftUI previews
/// This class mimics the behavior of LocationManager without actually using CoreLocation services
class MockLocationManager: ObservableObject {
    // Published properties to match the real LocationManager
    @Published var location: CLLocation? = CLLocation(latitude: 51.5074, longitude: -0.1278) // Default to London
    @Published var authorisationStatus: CLAuthorizationStatus = .authorizedWhenInUse
    @Published var accuracyAuthorisation: CLAccuracyAuthorization = .fullAccuracy
    @Published var isUpdatingLocation: Bool = false
    
    // Heading properties
    @Published var heading: CLHeading?
    @Published var headingAccuracy: Double = 5.0
    @Published var isUpdatingHeading: Bool = false
    
    // Properties accessible for settings
    @Published var distanceFilter: Double = 5.0
    @Published var activityType: CLActivityType = .fitness
    
    // GPS Signal Strength properties
    @Published var signalStrength: GPSSignalStrength = .good
    
    // Reference to DataManager
    private weak var dataManager: DataManager?
    
    // Initializer with DataManager
    init(dataManager: DataManager) {
        NSLog("MOCK_LM: Initializing MockLocationManager with DataManager")
        self.dataManager = dataManager
    }
    
    // Convenience initializer
    convenience init() {
        NSLog("MOCK_LM: Initializing MockLocationManager with convenience init")
        self.init(dataManager: DataManager())
    }
    
    // Mock methods that match the real LocationManager interface
    
    func requestAuthorisation() {
        NSLog("MOCK_LM: requestAuthorisation called - no action in mock")
        // Do nothing in mock
    }
    
    func startUpdatingLocation() {
        NSLog("MOCK_LM: startUpdatingLocation called")
        isUpdatingLocation = true
    }
    
    func stopUpdatingLocation() {
        NSLog("MOCK_LM: stopUpdatingLocation called")
        isUpdatingLocation = false
    }
    
    func startUpdatingHeading() {
        NSLog("MOCK_LM: startUpdatingHeading called")
        isUpdatingHeading = true
    }
    
    func stopUpdatingHeading() {
        NSLog("MOCK_LM: stopUpdatingHeading called")
        isUpdatingHeading = false
    }
    
    func updateSignalStrength() {
        NSLog("MOCK_LM: updateSignalStrength called")
        // In mock, we just keep the default good signal
    }
    
    func updateDistanceFilter(_ distance: Double) {
        NSLog("MOCK_LM: updateDistanceFilter called with \(distance)")
        distanceFilter = distance
    }
    
    func updateActivityType(_ activity: CLActivityType) {
        NSLog("MOCK_LM: updateActivityType called with \(activity)")
        activityType = activity
    }
}
