import SwiftUI
import UniformTypeIdentifiers
import UIKit
import CoreLocation

struct ExportView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var isSharing = false
    @State private var tempFileURL: URL?
    @State private var errorMessage: String?
    @State private var showingErrorAlert = false
    @State private var selectedFormat: ExportFormat = .gpx
    @State private var documentToShare: ShareableDocument? = nil

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                Text("Export Field Data")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top, 10)
                    .padding(.bottom, 40)
                    .foregroundColor(Color("TextColor")) // Theme text

                // Field Selection
                FieldSelectorView(showLabel: true, labelText: "Selected Field")
                    .padding(.horizontal)

                // Format Selection
                VStack(alignment: .leading) {
                    Text("Export Format")
                        .font(.headline)
                        .foregroundColor(Color("TextColor")) // Theme text
                    Picker("Format", selection: $selectedFormat) {
                        Text("GPX").tag(ExportFormat.gpx)
                            .foregroundColor(Color("TextColor")) // Theme picker item text
                        Text("KML").tag(ExportFormat.kml)
                            .foregroundColor(Color("TextColor")) // Theme picker item text
                        Text("CSV").tag(ExportFormat.csv)
                            .foregroundColor(Color("TextColor")) // Theme picker item text
                    }
                    .pickerStyle(.segmented)
                    .colorMultiply(Color("ThemePrimaryColor")) // Theme picker tint
                    .padding(.vertical, 5)
                }
                .padding(.horizontal)
                .padding(.bottom, 40)
                .accessibilityIdentifier("picker.exportformat")

                // Export Button - Styled like Mark Spot button in RecordingView
                Button(action: exportSelectedField) {
                    HStack {
                        Spacer()
                        Label("Export Data", systemImage: "square.and.arrow.up")
                            .font(.title3)
                            .padding()
                        Spacer()
                    }
                }
                .buttonStyle(.borderedProminent)
                .tint(Color("ThemePrimaryColor")) // Theme button
                .padding(.horizontal)
                .disabled(dataManager.selectedFieldID == nil) // Disable if no field selected
                .accessibilityIdentifier("button.export")

                Spacer() // Push content to the top
            }

            //.navigationTitle("Export")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(item: $documentToShare) { document in
                ShareSheet(activityItems: [document.url])
            }
            .alert("Export Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(errorMessage ?? "")
            }
            .accessibilityIdentifier("view.export")
        }
        .navigationViewStyle(.stack)
        .background(Color("BackgroundColor").ignoresSafeArea())
    }

    // Export functionality is now handled directly in exportSelectedField()

    private func prepareShare() {
        // Generate content based on selected format
        let content = generateExportContent()

        guard !content.isEmpty else {
            errorMessage = "No data available to export."
            showingErrorAlert = true
            return
        }

        let fileName = generateFileName()

        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        let fileURL = cacheDirectory.appendingPathComponent(fileName)

        do {
            try content.data(using: .utf8)?.write(to: fileURL)
            try FileManager.default.setAttributes([.posixPermissions: 0o644], ofItemAtPath: fileURL.path)
            tempFileURL = fileURL
            isSharing = true
            print("Created file for sharing at: \(fileURL.path)")
        } catch {
            print("Error creating file for sharing: \(error.localizedDescription)")
            errorMessage = error.localizedDescription
            showingErrorAlert = true
        }
    }

    private func cleanupTempFile() {
        if let fileURL = tempFileURL {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                if FileManager.default.fileExists(atPath: fileURL.path) {
                    do {
                        try FileManager.default.removeItem(at: fileURL)
                        print("Temporary file deleted: \(fileURL.path)")
                    } catch {
                        print("Error deleting temporary file: \(error.localizedDescription)")
                    }
                } else {
                    print("File already deleted or not found: \(fileURL.path)")
                }
            }
            tempFileURL = nil
        }
    }

    private func generateExportContent() -> String {
        // Get the selected field
        guard let fieldID = dataManager.selectedFieldID,
              let field = dataManager.fields.first(where: { $0.id == fieldID }),
              !field.geoPoints.isEmpty else {
            print("Warning: No field selected or field has no points.")
            return ""
        }

        // Generate content based on selected format
        switch selectedFormat {
        case .gpx:
            return GPXExporter.exportField(field)
        case .kml:
            return KMLExporter.exportField(field)
        case .csv:
            return CSVExporter.exportField(field)
        }
    }

    private func generateFileName() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let dateString = dateFormatter.string(from: Date())

        guard let fieldID = dataManager.selectedFieldID,
              let field = dataManager.fields.first(where: { $0.id == fieldID }) else {
            return "CropCompass_Export_\(dateString).\(selectedFormat.fileExtension)"
        }

        let safeFieldName = field.name.replacingOccurrences(of: " ", with: "_")
        return "\(safeFieldName)_\(dateString).\(selectedFormat.fileExtension)"
    }

    private func getSelectedField() -> Field? {
        guard let fieldID = dataManager.selectedFieldID else { return nil }
        return dataManager.fields.first(where: { $0.id == fieldID })
    }

    private func exportSelectedField() {
        // Generate content based on selected format
        let content = generateExportContent()

        guard !content.isEmpty else {
            errorMessage = "No data available to export."
            showingErrorAlert = true
            return
        }

        // Generate file name with appropriate extension
        let fileName = generateFileName()

        // Create a temporary file URL in the cache directory
        let cacheDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        let fileURL = cacheDirectory.appendingPathComponent(fileName)

        do {
            // Write the content to the file
            try content.data(using: .utf8)?.write(to: fileURL)
            try FileManager.default.setAttributes([.posixPermissions: 0o644], ofItemAtPath: fileURL.path)

            // Create a shareable document and show the share sheet
            documentToShare = ShareableDocument(url: fileURL)

            // Clean up the temporary file after a delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                if FileManager.default.fileExists(atPath: fileURL.path) {
                    do {
                        try FileManager.default.removeItem(at: fileURL)
                        print("Temporary file deleted: \(fileURL.path)")
                    } catch {
                        print("Error deleting temporary file: \(error.localizedDescription)")
                    }
                }
            }
        } catch {
            print("Error creating file for sharing: \(error.localizedDescription)")
            errorMessage = error.localizedDescription
            showingErrorAlert = true
        }
    }
}

private struct FieldRowView: View {
    let field: Field
    @Binding var selectedFields: Set<UUID>

    var body: some View {
        HStack {
            Text(field.name)
                .foregroundColor(Color("TextColor"))
            Spacer()
            if selectedFields.contains(field.id) {
                Image(systemName: "checkmark")
                    .foregroundColor(Color("ThemePrimaryColor"))
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            if selectedFields.contains(field.id) {
                selectedFields.remove(field.id)
            } else {
                selectedFields.insert(field.id)
            }
        }
    }
}

// FileDocument struct for handling GPX data
struct GPXDocument: FileDocument {
    static var readableContentTypes: [UTType] = [.gpx] // Use the UTType extension
    var gpxContent: String

    // Initializer for creating a document from content
    init(gpxContent: String) {
        self.gpxContent = gpxContent
    }

    // Initializer for reading a document from configuration
    init(configuration: ReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents,
              let string = String(data: data, encoding: .utf8)
        else {
            throw CocoaError(.fileReadCorruptFile)
        }
        gpxContent = string
    }

    // Generates a FileWrapper for writing the document
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = gpxContent.data(using: .utf8)!
        return .init(regularFileWithContents: data)
    }
}

// FileDocument struct for handling KML data
struct KMLDocument: FileDocument {
    static var readableContentTypes: [UTType] = [.kml] // Use the UTType extension
    var kmlContent: String

    // Initializer for creating a document from content
    init(kmlContent: String) {
        self.kmlContent = kmlContent
    }

    // Initializer for reading a document from configuration
    init(configuration: ReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents,
              let string = String(data: data, encoding: .utf8)
        else {
            throw CocoaError(.fileReadCorruptFile)
        }
        kmlContent = string
    }

    // Generates a FileWrapper for writing the document
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = kmlContent.data(using: .utf8)!
        return .init(regularFileWithContents: data)
    }
}

// FileDocument struct for handling CSV data
struct CSVDocument: FileDocument {
    static var readableContentTypes: [UTType] = [.commaSeparatedText] // Use the UTType extension
    var csvContent: String

    // Initializer for creating a document from content
    init(csvContent: String) {
        self.csvContent = csvContent
    }

    // Initializer for reading a document from configuration
    init(configuration: ReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents,
              let string = String(data: data, encoding: .utf8)
        else {
            throw CocoaError(.fileReadCorruptFile)
        }
        csvContent = string
    }

    // Generates a FileWrapper for writing the document
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = csvContent.data(using: .utf8)!
        return .init(regularFileWithContents: data)
    }
}

// ShareSheet implementation remains the same
struct ShareSheet: UIViewControllerRepresentable {
    var activityItems: [Any]
    var callback: (() -> Void)? = nil

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)

        controller.completionWithItemsHandler = { (activityType, completed, returnedItems, error) in
            if completed {
                print("Share completed: \(activityType?.rawValue ?? "unknown")")
                callback?()
            } else if let error = error {
                print("Share error: \(error.localizedDescription)")
            } else if activityType == nil {
                print("Share cancelled by user")
            }
        }

        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // Nothing to update
    }
}

#Preview {
    ExportView()
        .environmentObject(DataManager())
}
