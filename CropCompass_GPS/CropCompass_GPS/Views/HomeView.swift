//
//  HomeView.swift
//  CropCompass_GPS
//
//  Created by <PERSON><PERSON> on 2025-04-05.
//

import SwiftUI
import CoreLocation
import Foundation
import UserNotifications

struct HomeView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var locationManager: LocationManager
    @AppStorage("measurementSystem") private var measurementSystem: String = MeasurementSystem.metric.rawValue
    @State private var showingFieldManagement = false
    @State private var showingAddFieldSheet = false
    @State private var showingMarkSpotSheet = false
    @State private var newFieldName: String = "" // For AddFieldView sheet
    @State private var isRefreshing = false
    @State private var showingStartConfirmation = false // State for confirmation alert
    @State private var showingMarkConfirmation = false // State for Mark Spot confirmation

    // Area calculation result is now observed from dataManager.currentFieldArea

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // App Logo/Title - Use theme text color
                VStack {
                    Image(systemName: "location.circle.fill")
                        .font(.system(size: 60))
                        // Use ThemePrimaryColor for the logo icon
                        .foregroundColor(Color("ThemePrimaryColor"))
                        .shadow(color: .gray.opacity(0.3), radius: 2, x: 1, y: 1)

                    Text("CropCompass GPS")
                        .font(.title)
                        .fontWeight(.bold)
                        // Use theme text color
                        .foregroundColor(Color("TextColor"))
                }
                .padding(.top, 10)
                .padding(.bottom, 40)
                .padding(.horizontal) // Add horizontal padding here

                // Quick Action Buttons - Apply tint
                quickActionButtons
                    .padding(.horizontal) // Add horizontal padding here

                // Farm and Field Selection Section - Add horizontal padding back
                farmFieldSelectionSection
                    .padding(.horizontal) // Add horizontal padding here

                // Stats Section (for selected field) - Renamed from statsSection
                selectedFieldStatsSection
                    .padding(.horizontal) // Add horizontal padding here

                // --- Manage Fields Button Moved Below Stats ---
                Button {
                    showingFieldManagement = true
                } label: {
                    Label("Manage Fields", systemImage: "list.bullet.clipboard")
                        .font(.headline) // Match other button styles if needed
                        .frame(maxWidth: .infinity)
                        .padding()
                }
                .buttonStyle(.bordered) // Use bordered style for less prominence than actions
                .tint(Color("ThemeSecondaryColor")) // Use secondary theme colour
                .padding(.horizontal) // Add horizontal padding here
                .accessibilityIdentifier("button.manage.fields")
                // --- End Manage Fields Button ---

                // Recent Activity Section -- REMOVED
                // Weather Information Section -- REMOVED

                Spacer() // Add Spacer to push content up
            }

            .refreshable {
                // Simulate a refresh
                isRefreshing = true
                // Add any refresh logic here
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    isRefreshing = false
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            // Sheet for Field Management
            .sheet(isPresented: $showingFieldManagement) {
                FieldManagementView()
                    .environmentObject(dataManager)
            }

            // Sheet for Adding a new Field
            .sheet(isPresented: $showingAddFieldSheet) {
                AddFieldView(fieldName: $newFieldName) {
                    let nameToSave = newFieldName.trimmingCharacters(in: .whitespaces)
                    guard !nameToSave.isEmpty else { return }
                    _ = dataManager.createField(name: nameToSave)
                    newFieldName = "" // Clear after save
                }
                .environmentObject(dataManager)
            }

            // Field selection is now handled by the FieldSelectorView component
            // Sheet for Mark Spot
            .sheet(isPresented: $showingMarkSpotSheet) {
                if let currentLocation = locationManager.location,
                   let currentFieldID = dataManager.selectedFieldID {
                    MarkSpotNoteView(
                        location: currentLocation,
                        fieldID: currentFieldID
                    ) { note, pointType in
                        _ = dataManager.addGeoPoint(
                            coordinate: currentLocation.coordinate,
                            elevation: currentLocation.altitude,
                            timestamp: currentLocation.timestamp,
                            type: pointType,
                            note: note,
                            to: currentFieldID
                        )
                    }
                } else {
                    // Fallback if location or selection is missing
                    VStack {
                        Text("Error")
                            .font(.title)
                        Text("Cannot mark spot: Missing location data or field selection.")
                            .padding()
                        Button("OK") { showingMarkSpotSheet = false }
                            .buttonStyle(.borderedProminent)
                    }
                }
            }
        }
        .navigationViewStyle(.stack)
        .background(Color("BackgroundColor").ignoresSafeArea())
        .accessibilityIdentifier("view.home")
        // ADD alerts for HomeView
        .alert("Field Has Data", isPresented: $showingStartConfirmation) {
            Button("Continue Recording") {
                startRecordingFlow()
            }
            Button("Create New Field") {
                showingAddFieldSheet = true
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This field already has recorded points. Do you want to continue adding points to this field or create a new one?")
        }
        .alert("Field Has Continuous Points", isPresented: $showingMarkConfirmation) {
            Button("Mark Spot Here") {
                showingMarkSpotSheet = true
            }
            Button("Create New Field") {
                showingAddFieldSheet = true
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This field already has continuous tracking points. Do you want to mark a new spot in this field or create a new field first?")
        }
    }

    // Quick Action Buttons
    private var quickActionButtons: some View {
        HStack(spacing: 15) {
            // Start/Stop Recording Button
            Button {
                if dataManager.selectedFieldID != nil {
                    if dataManager.recordingState == .notRecording {
                        // Check if the selected field contains continuous points
                        if let fieldID = dataManager.selectedFieldID, dataManager.fieldContainsContinuousPoints(fieldId: fieldID) {
                            // Field has continuous points, show confirmation
                            showingStartConfirmation = true
                        } else {
                            // Field is empty or has only marked/boundary points, start directly
                            startRecordingFlow()
                        }
                    } else {
                        // Stop recording
                        dataManager.stopContinuousRecording()
                    }
                }
            } label: {
                VStack {
                    Image(systemName: dataManager.recordingState == .notRecording ? "record.circle" : "stop.circle")
                        .font(.system(size: 30))
                        // Icon color will be handled by tint
                    Text(dataManager.recordingState == .notRecording ? "Start Recording" : "Stop Recording")
                        .font(.caption)
                }
                .frame(maxWidth: .infinity)
                .padding()
                // Background handled by button style
            }
            // Use borderedProminent style with appropriate tint
            .buttonStyle(.borderedProminent)
            .tint(dataManager.recordingState == .notRecording ? Color("ThemePrimaryColor") : Color("ErrorColor"))
            .disabled(dataManager.selectedFieldID == nil)
            .opacity(dataManager.selectedFieldID == nil ? 0.6 : 1.0)
            .accessibilityIdentifier(dataManager.recordingState == .notRecording ? "button.recording.start" : "button.recording.stop")

            // Mark Spot Button
            Button {
                guard locationManager.location != nil, dataManager.selectedFieldID != nil else {
                    // Maybe show an alert if location is missing?
                    return
                }

                // Check conditions to bypass confirmation
                if dataManager.recordingState == .recordingContinuous {
                    // When recording is in progress, bypass confirmation
                    showingMarkSpotSheet = true
                } else if let fieldID = dataManager.selectedFieldID, dataManager.fieldContainsOnlyMarkedSpots(fieldId: fieldID) {
                    // When field only contains marked spots (no continuous points), bypass confirmation
                    showingMarkSpotSheet = true
                } else if let selectedField = dataManager.selectedField, !selectedField.geoPoints.isEmpty {
                    // Field has continuous data and not recording, show confirmation
                    showingMarkConfirmation = true
                } else {
                     // Field is empty, show sheet directly
                    showingMarkSpotSheet = true
                }
            } label: {
                VStack {
                    Image(systemName: "mappin.and.ellipse")
                        .font(.system(size: 30))
                        // Icon color handled by tint
                    Text("Mark Spot")
                        .font(.caption)
                }
                .frame(maxWidth: .infinity)
                .padding()
                // Background handled by button style
            }
            // Use borderedProminent style with ThemeSecondaryColor tint
            .buttonStyle(.borderedProminent)
            .tint(Color("ThemeSecondaryColor"))
            .disabled(dataManager.selectedFieldID == nil || locationManager.location == nil)
            .opacity((dataManager.selectedFieldID == nil || locationManager.location == nil) ? 0.6 : 1.0)
            .accessibilityIdentifier("button.point.save")
        }
    }

    // Farm and Field Selection Section with FieldSelectorView Component
    private var farmFieldSelectionSection: some View {
        VStack(spacing: 8) {
            // Title outside the background
            Text("Selected Field")
                .font(.subheadline)
                .foregroundColor(Color("TextLightColor"))
                .frame(maxWidth: .infinity, alignment: .leading)
                //.padding(.horizontal)

            // Field selector without label
            VStack {
                // Using FieldSelectorView without label parameter
                FieldSelectorView(showLabel: false)
                    //.padding(.horizontal)
            }
            .padding(.vertical, 8)
            .background(Color("CardBackgroundColor"))
            .cornerRadius(10)
            //.padding(.horizontal)
        }
    }

    // Stats Section (for selected field) - Renamed and kept as is
    @ViewBuilder
    private var selectedFieldStatsSection: some View {
        if let selectedField = dataManager.selectedField {
            VStack(spacing: 8) {
                // Title outside the background
                Text("Field Statistics")
                    .font(.subheadline)
                    .foregroundColor(Color("TextLightColor"))
                    .frame(maxWidth: .infinity, alignment: .leading)

                // Stats content inside the background
                VStack(alignment: .leading, spacing: 10) {
                    // Field Name
                    Text(selectedField.name)
                        .font(.headline)
                        .foregroundColor(Color("TextColor"))

                    Divider()

                    // Area Stat - Observe from DataManager
                    HStack {
                        Image(systemName: "square.dashed")
                            .foregroundColor(Color("ThemePrimaryColor"))
                        Text("Area:")
                            .foregroundColor(Color("TextColor"))
                        Spacer()
                        // Observe the value from DataManager
                        Text(formatArea(dataManager.currentFieldArea))
                            .foregroundColor(Color("TextColor"))
                            .accessibilityIdentifier("label.area")
                    }

                    // Points Stat
                    HStack {
                        Image(systemName: "mappin.and.ellipse")
                            .foregroundColor(Color("ThemeSecondaryColor"))
                        Text("Points:")
                            .foregroundColor(Color("TextColor"))
                        Spacer()
                        // Correctly count only geoPoints as spotPoints does not exist on Field model
                        Text("\(selectedField.geoPoints.count)")
                            .foregroundColor(Color("TextColor"))
                            .accessibilityIdentifier("label.points.count")
                    }

                    // Distance Stat (Optional, calculate if needed)
                    // You might want to apply a similar @State pattern for distance
                    // if calculateTotalDistance is also expensive or called too often.
                    let distance = dataManager.calculateTotalDistance(fieldId: selectedField.id)
                    if distance > 0 {
                        HStack {
                            Image(systemName: "point.topleft.down.curvedto.point.bottomright.up.fill")
                                .foregroundColor(Color("WarningColor"))
                            Text("Track Length:")
                                .foregroundColor(Color("TextColor"))
                            Spacer()
                            Text(formatDistance(distance))
                                .foregroundColor(Color("TextColor"))
                                .accessibilityIdentifier("label.distance")
                        }
                    }
                }
                .padding()
                .background(Color("CardBackgroundColor"))
                .cornerRadius(10)
            }
            .accessibilityElement(children: .contain)
            .accessibilityIdentifier("section.stats")
        } else {
            // Placeholder when no field is selected
            Text("Select a field to see statistics.")
                .font(.footnote)
                .foregroundColor(Color("TextLightColor"))
                .padding()
                .accessibilityIdentifier("label.nostats")
        }
    }

    // Helper function to start the actual recording
    private func startRecordingFlow() {
        // Request authorization first to ensure we have the right permissions
        locationManager.requestAuthorisation()

        // Start location updates if not already running
        if !locationManager.isUpdatingLocation {
            locationManager.startUpdatingLocation()
        }

        // Start recording in the data manager
        dataManager.startContinuousRecording()

        // Inform the user that recording will continue in the background
        if locationManager.authorisationStatus == .authorizedAlways {
            // Only show this message if we have "Always" authorization
            let content = UNMutableNotificationContent()
            content.title = "Recording in Progress"
            content.body = "GPS recording will continue in the background"
            content.sound = .default

            // Create a notification request
            let request = UNNotificationRequest(identifier: "backgroundRecording", content: content, trigger: nil)

            // Add the request to the notification center
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("Error showing notification: \(error.localizedDescription)")
                }
            }
        }
    }

    // Helper formatters
    private func formatDistance(_ distance: Double) -> String {
        return UnitConverter.formatDistance(distance, system: MeasurementSystem(rawValue: measurementSystem) ?? .metric)
    }

    // Restore formatArea function as it's needed again
    private func formatArea(_ area: Double?) -> String {
        guard let area = area else { return "--" } // Handle nil case
        return UnitConverter.formatArea(area, system: MeasurementSystem(rawValue: measurementSystem) ?? .metric)
    }
}

// Helper view for stats items
struct StatItemView: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 5) {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(color)

            Text(value)
                .font(.subheadline)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct HomeView_Previews: PreviewProvider {
    static var previews: some View {
        HomeView()
            .environmentObject(DataManager())
            .environmentObject(LocationManager(dataManager: DataManager()))
    }
}
