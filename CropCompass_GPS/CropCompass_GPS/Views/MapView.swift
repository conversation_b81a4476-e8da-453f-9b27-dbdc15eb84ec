import SwiftUI
import MapKit

struct MapView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var locationManager: LocationManager
    @State private var cameraPosition: MapCameraPosition = .automatic
    @State private var selectedPoint: GeoPoint? = nil
    @AppStorage("mapStyleChoice") private var mapStyleChoiceValue: String = MapStyleChoice.standard.rawValue // Use AppStorage
    @AppStorage("measurementSystem") private var measurementSystem: String = MeasurementSystem.metric.rawValue
    @State private var visibleRegion: MKCoordinateRegion? // State for visible region
    @State private var mapHeading: Double = 0           // State for map heading
    @State private var selectedPointTag: UUID? = nil

    // Computed property to get the actual MapStyle based on AppStorage choice
    private var currentMapStyle: MapStyle {
        MapStyleChoice(rawValue: mapStyleChoiceValue)?.mapKitStyle ?? .standard
    }

    var body: some View {
        NavigationView {
            ZStack {
                // Main background for the entire view
                Color("BackgroundColor")
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    if let field = dataManager.selectedField {
                        // Header with transparent background
                        MapViewHeader(field: field)

                        // Map Content with transparent background
                        MapContentView(
                            selectedField: field,
                            mapCameraPosition: $cameraPosition,
                            selectedPointId: $selectedPointTag,
                            mapStyle: currentMapStyle,
                            visibleRegion: $visibleRegion,
                            mapHeading: $mapHeading,
                            recenterAction: recenterMapOnField
                        )
                        // No background modifier here
                        .environmentObject(locationManager)
                        .sheet(item: $selectedPoint) { point in
                            PointDetailsEditView(point: point)
                                .environmentObject(dataManager)
                                .presentationDetents([.medium, .large])
                        }

                        // Info Bar (Observes DataManager area)
                        MapViewInfoBar(
                            field: field,
                            mapStyleChoiceValue: $mapStyleChoiceValue,
                            totalDistance: calculateDistance(for: field.geoPoints),
                            area: dataManager.currentFieldArea, // Observe from DataManager
                            recenterAction: recenterMapOnField
                        )
                    } else {
                        // Placeholder
                        ContentUnavailableView(
                            "No Field Selected",
                            systemImage: "map.fill",
                            description: Text("Select a field from the Home or Recording screen to view it on the map.")
                        )
                    }
                }
            }
            .navigationTitle("Field Map")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // Only show button if a field IS selected
                if dataManager.selectedField != nil {
                    // REMOVE Recenter Button from Toolbar
                    /*
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button {
                            // Action to re-center the map
                            recenterMapOnField()
                        } label: {
                            Label("Recenter", systemImage: "location.fill")
                        }
                        .accessibilityIdentifier("button.recenter")
                    }
                    */
                }
            }
            .onChange(of: dataManager.selectedFieldID) { _, _ in
                // Recenter map when selected field changes
                recenterMapOnField()
                selectedPointTag = nil // Clear selection when field changes
            }
            .onChange(of: selectedPointTag) { _, newValue in
                // Update the state variable to show the sheet when a point is tapped
                if let pointId = newValue,
                   let field = dataManager.selectedField,
                   let pointToShow = field.geoPoints.first(where: { $0.id == pointId }) {
                    selectedPoint = pointToShow
                } else {
                    selectedPoint = nil
                }
            }
            // Area calculation is now handled by HomeView and observed via DataManager
        }
        .navigationViewStyle(.stack)
    }

    // MARK: - Helper Functions for Display

    // Helper function to calculate bounding box for all points
    private func getBoundingBox(for points: [GeoPoint]) -> MKCoordinateRegion? {
        guard !points.isEmpty else { return nil }

        var minLat = points[0].latitude
        var maxLat = points[0].latitude
        var minLon = points[0].longitude
        var maxLon = points[0].longitude

        for point in points {
            minLat = min(minLat, point.latitude)
            maxLat = max(maxLat, point.latitude)
            minLon = min(minLon, point.longitude)
            maxLon = max(maxLon, point.longitude)
        }

        // Add some padding
        let latDelta = max(0.005, maxLat - minLat) * 1.2
        let lonDelta = max(0.005, maxLon - minLon) * 1.2

        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )

        return MKCoordinateRegion(
            center: center,
            span: MKCoordinateSpan(
                latitudeDelta: latDelta,
                longitudeDelta: lonDelta
            )
        )
    }

    // Calculate total distance of the track
    private func calculateDistance(for points: [GeoPoint]) -> Double {
        guard points.count > 1 else { return 0 }

        var totalDistance = 0.0

        for i in 0..<points.count-1 {
            let point1 = CLLocation(
                latitude: points[i].latitude,
                longitude: points[i].longitude
            )

            let point2 = CLLocation(
                latitude: points[i+1].latitude,
                longitude: points[i+1].longitude
            )

            totalDistance += point1.distance(from: point2)
        }

        return totalDistance
    }

    // Format distance for display using the current measurement system
    private func formatDistance(_ distance: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        return UnitConverter.formatDistance(distance, system: system)
    }

    // Format area for display using the current measurement system
    private func formatArea(_ area: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        return UnitConverter.formatArea(area, system: system)
    }

    private func recenterMapOnField() {
        if let field = dataManager.selectedField {
            // If field has points, center on them
            if !field.geoPoints.isEmpty,
               let bounds = getBoundingBox(for: field.geoPoints) {
                print("Recenter: Centering on field bounds.")
                cameraPosition = .region(bounds)
                visibleRegion = bounds // Update state here
            }
            // If field is empty, center on user location if available
            else if let userLocation = locationManager.location {
                print("Recenter: Centering on user location (empty field).")
                let camera = MapCamera(centerCoordinate: userLocation.coordinate, distance: 500) // Zoom in to 500m
                cameraPosition = .camera(camera)
                // Optionally update visibleRegion based on user location and zoom?
                // visibleRegion = MKCoordinateRegion(center: userLocation.coordinate, latitudinalMeters: 500, longitudinalMeters: 500)
            }
            // If field is empty and no user location, map defaults to automatic
            else {
                print("Recenter: Field empty, no user location. Using automatic position.")
                cameraPosition = .automatic // Fallback if no points and no user location
            }
        } else {
            // If no field is selected, center on user location if available
            if let userLocation = locationManager.location {
                 print("Recenter: Centering on user location (no field selected).")
                 let camera = MapCamera(centerCoordinate: userLocation.coordinate, distance: 500)
                 cameraPosition = .camera(camera)
            } else {
                 print("Recenter: No field selected, no user location. Using automatic position.")
                 cameraPosition = .automatic // Fallback if no field and no user location
            }
        }
    }
}

// MARK: - Subviews

/// Extracted View for the top header containing field selection
private struct MapViewHeader: View {
    let field: Field

    var body: some View {
        FieldSelectorView(showLabel: true, labelText: "Selected Field")
            .padding(.horizontal)
            .padding(.top, 8)
    }
}

/// Extracted View for the main map content and its overlays
private struct MapContentView: View {
    let selectedField: Field
    @Binding var mapCameraPosition: MapCameraPosition
    @Binding var selectedPointId: UUID?
    let mapStyle: MapStyle // Accept mapStyle as a parameter
    @Binding var visibleRegion: MKCoordinateRegion? // Accept binding
    @Binding var mapHeading: Double               // Accept binding
    let recenterAction: () -> Void              // Action to recenter the map

    @EnvironmentObject var dataManager: DataManager // Still needed for field data
    @EnvironmentObject var locationManager: LocationManager // Add LocationManager access

    var body: some View {
        ZStack(alignment: .top) {
            // The actual map with all layers
            Map(position: $mapCameraPosition, interactionModes: .all, selection: $selectedPointId) {
                // Use the mapLayers content builder to generate all map content
                mapLayers
            }
            .mapStyle(mapStyle)
            .ignoresSafeArea()
            .onAppear {
                // Call the passed-in recenter action on appear
                recenterAction()
            }
            .onMapCameraChange { context in
                // Update the map heading when the camera changes
                // context.camera is not optional, so we can access it directly
                mapHeading = context.camera.heading
            }

            // Add the MapViewNorthControl as an invisible helper
            MapViewNorthControl(setNorth: false, cameraPosition: $mapCameraPosition, visibleRegion: visibleRegion)
                .frame(width: 0, height: 0)

            // Bearing overlay at the top
            VStack {
                MapBearingOverlay(mapHeading: mapHeading)
                    .padding(.top, 10)
                Spacer()
            }
            .frame(maxWidth: .infinity)
            .allowsHitTesting(false)
        }
    }

    // Computed property to generate map layers - THEME MARKERS & OVERLAYS
    @MapContentBuilder var mapLayers: some MapContent {
        let points = selectedField.geoPoints

        // Draw the track line - Use ThemePrimaryColor
        if points.count > 1 {
            MapPolyline(coordinates: points.map { CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) })
            .stroke(Color("ThemePrimaryColor"), lineWidth: 3)
        }

        // Draw area polygon - Use ThemePrimaryColor
        if points.count >= 3 {
            let coordinates = points.map { CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) }
            MapPolygon(coordinates: coordinates)
                .foregroundStyle(Color("ThemePrimaryColor").opacity(0.15))
        }

        // Show markers for all recorded field points - Theme markers
        ForEach(points, id: \.id) { (point: GeoPoint) in
            // Use theme colors for marker tints
            let color: Color = switch point.pointType {
                case .marked: Color("AccentColor")
                case .boundary: Color("WarningColor")
                case .continuous: Color("ThemePrimaryColor")
            }
            // Icons remain the same
            let icon: String = switch point.pointType { case .marked: "mappin"; case .boundary: "square.fill"; case .continuous: "circle.fill" }

            let labelText = point.note ?? "Point \(point.timestamp.formatted(date: .omitted, time: .shortened))"

            Marker(coordinate: CLLocationCoordinate2D(latitude: point.latitude, longitude: point.longitude),
                   label: {
                        // Theme Label text
                        Label(labelText, systemImage: icon)
                            .foregroundColor(Color("TextColor"))
                   })
            .tint(color) // Apply themed tint
            .tag(point.id)
        }

        // Show marker for current user location
        if let userLocation = locationManager.location {
            Marker("Current Location", systemImage: "figure.walk.circle.fill", coordinate: userLocation.coordinate)
                .tint(.blue) // Distinct colour for user location
        }
    }

    // Private helper needed within this view
    private func getBoundingBox(for points: [GeoPoint]) -> MKCoordinateRegion? {
        guard !points.isEmpty else { return nil }
        var minLat = points[0].latitude; var maxLat = points[0].latitude
        var minLon = points[0].longitude; var maxLon = points[0].longitude
        for point in points {
            minLat = min(minLat, point.latitude); maxLat = max(maxLat, point.latitude)
            minLon = min(minLon, point.longitude); maxLon = max(maxLon, point.longitude)
        }
        let latDelta = max(0.005, maxLat - minLat) * 1.2
        let lonDelta = max(0.005, maxLon - minLon) * 1.2
        let center = CLLocationCoordinate2D(latitude: (minLat + maxLat) / 2, longitude: (minLon + maxLon) / 2)
        return MKCoordinateRegion(center: center, span: MKCoordinateSpan(latitudeDelta: latDelta, longitudeDelta: lonDelta))
    }
}

// RE-ADD MapViewInfoBar definition
private struct MapViewInfoBar: View {
    let field: Field
    @Binding var mapStyleChoiceValue: String
    let totalDistance: Double
    let area: Double? // Accepts optional area now
    let recenterAction: () -> Void
    @AppStorage("measurementSystem") private var measurementSystem: String = MeasurementSystem.metric.rawValue

    var body: some View {
        HStack {
            // Recenter Button
            Button {
                recenterAction()
            } label: {
                Image(systemName: "viewfinder")
                    .font(.title3) // Give it a reasonable size
            }
            .padding(5) // Add some padding around the icon for touch area
            .tint(Color("ThemePrimaryColor")) // Style like the picker
            .accessibilityIdentifier("button.recenter.infobar")
            .help("Recenter map on field")

            // Distance Display
            VStack(alignment: .leading) {
                Text("Distance").font(.caption).foregroundColor(Color("TextLightColor"))
                Text(formatDistance(totalDistance)).font(.subheadline).foregroundColor(Color("TextColor"))
            }

            Spacer()

            // Area Display - Uses the passed optional area
            VStack(alignment: .leading) {
                Text("Area").font(.caption).foregroundColor(Color("TextLightColor"))
                Text(formatArea(area)).font(.subheadline).foregroundColor(Color("TextColor")) // FormatArea handles nil
            }

            Spacer()

            // Map Style Picker - Manually list options
            Picker("Map Style", selection: $mapStyleChoiceValue) {
                // Manually list each option to avoid ForEach inference issues
                Image(systemName: MapStyleChoice.standard.iconName)
                    .tag(MapStyleChoice.standard.rawValue)

                Image(systemName: MapStyleChoice.hybrid.iconName)
                    .tag(MapStyleChoice.hybrid.rawValue)

                Image(systemName: MapStyleChoice.imagery.iconName)
                    .tag(MapStyleChoice.imagery.rawValue)
            }
            .pickerStyle(.segmented)
            .frame(maxWidth: 150) // Constrain width
            .accessibilityIdentifier("picker.mapstyle.quick")
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(.ultraThinMaterial) // Use material background
    }

    // Helper formatters (should be consistent with MapView's or moved to UnitConverter)
    private func formatDistance(_ distance: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        return UnitConverter.formatDistance(distance, system: system)
    }

    private func formatArea(_ area: Double?) -> String {
        guard let area = area else { return "--" }
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        return UnitConverter.formatArea(area, system: system)
    }
}

#Preview {
    // ... (Existing MapView Preview setup)
    let dataManager = DataManager()
    _ = dataManager.createField(name: "Map Preview Field")
    // Add dummy points if needed for preview calculations

    return MapView()
        .environmentObject(LocationManager(dataManager: dataManager))
        .environmentObject(dataManager)
}
