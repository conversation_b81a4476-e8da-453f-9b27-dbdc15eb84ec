//
//  ContentView.swift
//  CropCompass_GPS
//
//  Created by <PERSON><PERSON> on 28/3/2025.
//

import SwiftUI
import CoreLocation
// Import all views used in tabs

// Helper for detecting SwiftUI previews
struct PreviewDetector {
    // Check if we're running in preview mode
    static var isPreview: Bool {
        // Check 1: Process name contains "previews"
        let processName = ProcessInfo.processInfo.processName.lowercased()
        if processName.contains("preview") {
            return true
        }

        // Check 2: Environment variable check
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return true
        }

        // Check 3: Check for PreviewsInjection framework in loaded bundles
        for bundle in Bundle.allBundles {
            let bundlePath = bundle.bundlePath.lowercased()
            if bundlePath.contains("previewsinjection") {
                return true
            }
        }

        return false
    }
}

struct ContentView: View {
    @EnvironmentObject var locationManager: LocationManager
    @EnvironmentObject var dataManager: DataManager
    @State private var showingFieldManagement = false
    @State private var showingMarkSpotSheet = false
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // Home Tab
            HomeView()
                .tabItem {
                    Label("Home", systemImage: "house.fill")
                }
                .tag(0)
                .accessibilityIdentifier("tab.home")

            // Recording Tab
            RecordingView()
                .tabItem {
                    Label("Record", systemImage: "record.circle")
                }
                .tag(1)
                .accessibilityIdentifier("tab.record")

            // Map Tab
            MapView()
                .tabItem {
                    Label("Map", systemImage: "map")
                }
                .tag(2)
                .accessibilityIdentifier("tab.map")

            // Export Tab
            ExportView()
                .tabItem {
                    Label("Export", systemImage: "square.and.arrow.up")
                }
                .tag(3)
                .accessibilityIdentifier("tab.export")

            // Settings Tab
            SettingsView()
                .tabItem {
                    Label("Settings", systemImage: "gear")
                }
                .tag(4)
                .accessibilityIdentifier("tab.settings")
        }
        .onAppear {
            NSLog("ContentView_DEBUG: .onAppear triggered.")
        }
    }
}

// All sub-views (RecordingView, MapView, etc.) are now in the Views/ directory.

#Preview {
    // Create a shared DataManager instance
    let dataManager = DataManager()

    // Use the actual LocationManager for previews to provide the correct environment object type
    return ContentView()
        .environmentObject(LocationManager(dataManager: dataManager))
        .environmentObject(dataManager)
}
