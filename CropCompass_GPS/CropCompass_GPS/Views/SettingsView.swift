import SwiftUI
import CoreLocation

struct SettingsView: View {
    @AppStorage("measurementSystem") var measurementSystem: String = MeasurementSystem.metric.rawValue
    @EnvironmentObject var locationManager: LocationManager
    @State private var selectedDistanceFilter = 5.0
    @State private var selectedActivityType = CLActivityType.fitness

    // Distance options will be determined based on the measurement system
    let activityOptions: [(String, CLActivityType)] = [
        ("Walking", .fitness),
        ("Cycling", .other),
        ("Vehicle", .automotiveNavigation),
        ("Tractor", .otherNavigation)
    ]

    var body: some View {
        NavigationView {
            Form {
                // GPS Settings (Moved to Top)
                Section(header: Text("GPS Settings").foregroundColor(Color("TextLightColor"))) {
                    // Activity Type
                    Picker("Activity Type", selection: $selectedActivityType) {
                        ForEach(activityOptions, id: \.0) { option in
                            Text(option.0).tag(option.1)
                                .foregroundColor(Color("TextColor"))
                        }
                    }
                    .accessibilityIdentifier("picker.activitytype")
                    .onChange(of: selectedActivityType) { oldValue, newValue in
                        print("Activity type changed from \(oldValue) to \(newValue)")
                        locationManager.updateActivityType(newValue)
                    }

                    // Point Recording Interval
                    Picker("Recording Interval", selection: $selectedDistanceFilter) {
                        ForEach(distanceFilterOptions, id: \.self) { distance in
                            Text(formatDistanceForPicker(distance)).tag(distance)
                                .foregroundColor(Color("TextColor"))
                        }
                    }
                    .accessibilityIdentifier("picker.distancefilter")
                    .onChange(of: selectedDistanceFilter) { _, newValue in
                        // Convert to meters if using imperial
                        let distanceInMeters = currentMeasurementSystem == .imperial ?
                            UnitConverter.distanceFilterToMeters(newValue, from: .imperial) : newValue
                        locationManager.updateDistanceFilter(distanceInMeters)
                    }
                }
                .listRowBackground(Color("BackgroundColor"))

                // Map Settings Section REMOVED
                /*
                Section(header: Text("Map Settings").foregroundColor(Color("TextLightColor"))) {
                    Picker("Map Style", selection: $mapStyleChoice) {
                        ForEach(MapStyleChoice.allCases) { style in
                            Text(style.displayName).tag(style.rawValue)
                                .foregroundColor(Color("TextColor"))
                        }
                    }
                    .accessibilityIdentifier("picker.mapstyle")
                }
                .listRowBackground(Color("BackgroundColor"))
                */

                // Unit Settings
                Section(header: Text("Unit Settings").foregroundColor(Color("TextLightColor"))) {
                    // RENAMED Picker Label to "System"
                    Picker("System", selection: $measurementSystem) {
                        ForEach(MeasurementSystem.allCases) { system in
                            Text(system.displayName).tag(system.rawValue)
                                .foregroundColor(.white)
                        }
                    }
                    .accessibilityIdentifier("picker.measurementsystem")
                    .onChange(of: measurementSystem) { _, _ in
                        // Update distance filter options when measurement system changes
                        updateDistanceFilterForCurrentSystem()
                    }
                }
                .listRowBackground(Color("BackgroundColor"))

                // Location Settings
                Section(header: Text("Location Settings").foregroundColor(Color("TextLightColor"))) {
                    Toggle("Enable Location Services", isOn: $locationManager.isUpdatingLocation)
                        .tint(Color("ThemePrimaryColor"))
                        .foregroundColor(Color("TextColor"))
                        .accessibilityIdentifier("toggle.locationservices")
                }
                .listRowBackground(Color("BackgroundColor"))

                // GPS Settings Section MOVED to top

                Section(header: Text("Location Authorization").foregroundColor(Color("TextLightColor"))) {
                    VStack(alignment: .leading) {
                        Text("Current Status:")
                            .foregroundColor(Color("TextColor"))
                        Text(locationManager.authorisationStatus.description)
                            .foregroundColor(
                                locationManager.authorisationStatus == .authorizedAlways ||
                                locationManager.authorisationStatus == .authorizedWhenInUse ?
                                Color("SuccessColor") : Color("ErrorColor")
                            )
                            .bold()
                            .accessibilityIdentifier("label.authstatus")
                    }

                    Button("Request Location Permission") {
                        locationManager.requestAuthorisation()
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(Color("ThemePrimaryColor"))
                    .accessibilityIdentifier("button.requestauth")
                }
                .listRowBackground(Color("BackgroundColor"))

                Section(header: Text("About").foregroundColor(Color("TextLightColor"))) {
                    HStack {
                        Text("Version")
                            .foregroundColor(Color("TextColor"))
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(Color("TextLightColor"))
                    }

                    HStack {
                        Text("Build")
                            .foregroundColor(Color("TextColor"))
                        Spacer()
                        Text("2025.04.02")
                            .foregroundColor(Color("TextLightColor"))
                    }
                }
                .listRowBackground(Color("BackgroundColor"))
            }
            .scrollContentBackground(.hidden)
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .accessibilityIdentifier("view.settings")
        }
        .background(Color("BackgroundColor").ignoresSafeArea())
        .onAppear {
            // Set initial values from the location manager
            updateDistanceFilterForCurrentSystem()
            selectedActivityType = locationManager.activityType
            print("SettingsView appeared with activity type: \(selectedActivityType)")
        }
    }
}

// MARK: - Helper Methods

extension SettingsView {

    /// Get the current measurement system as an enum
    var currentMeasurementSystem: MeasurementSystem {
        return MeasurementSystem(rawValue: measurementSystem) ?? .metric
    }

    /// Get the appropriate distance filter options based on the current measurement system
    var distanceFilterOptions: [Double] {
        return UnitConverter.distanceFilterOptions(for: currentMeasurementSystem)
    }

    /// Format a distance value for display in the picker
    /// - Parameter distance: The distance value
    /// - Returns: A formatted string with the appropriate unit
    func formatDistanceForPicker(_ distance: Double) -> String {
        return UnitConverter.formatDistanceForPicker(distance, system: currentMeasurementSystem)
    }

    /// Update the distance filter value when the measurement system changes
    func updateDistanceFilterForCurrentSystem() {
        // Get the current distance filter in meters from the location manager
        let currentFilterInMeters = locationManager.distanceFilter

        // If we're using imperial, convert from meters to feet
        if currentMeasurementSystem == .imperial {
            // Convert the current filter from meters to feet
            let currentFilterInFeet = currentFilterInMeters * 3.28084

            // Find the closest match in the imperial options
            let imperialOptions = UnitConverter.distanceFilterOptions(for: .imperial)
            let closestOption = imperialOptions.min(by: { abs($0 - currentFilterInFeet) < abs($1 - currentFilterInFeet) }) ?? 5.0

            selectedDistanceFilter = closestOption
        } else {
            // Find the closest match in the metric options
            let metricOptions = UnitConverter.distanceFilterOptions(for: .metric)
            let closestOption = metricOptions.min(by: { abs($0 - currentFilterInMeters) < abs($1 - currentFilterInMeters) }) ?? 5.0

            selectedDistanceFilter = closestOption
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(LocationManager(dataManager: DataManager()))
        .environmentObject(DataManager()) // Added DataManager for Preview consistency
}