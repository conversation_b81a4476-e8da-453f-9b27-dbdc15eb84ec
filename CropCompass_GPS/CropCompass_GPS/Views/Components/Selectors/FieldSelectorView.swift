import SwiftUI

struct FieldSelectorView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var showingAddFieldSheet = false
    @State private var newFieldName: String = ""

    // Optional parameters for customization
    var showLabel: Bool = true
    var labelText: String = "Selected Field"

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Label is now always outside the selection box
            if showLabel {
                Text(labelText)
                    .font(.subheadline)
                    .foregroundColor(Color("TextLightColor"))
                    .padding(.horizontal, 4)
            }

            // Field selector
            if dataManager.fields.isEmpty {
                // No fields available - Theme this button
                Button {
                    showingAddFieldSheet = true
                } label: {
                    HStack {
                        Text("Add Your First Field")
                            .foregroundColor(Color("ThemePrimaryColor"))
                        Spacer()
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(Color("ThemePrimaryColor"))
                    }
                    .padding(12)
                    .background(Color("CardBackgroundColor"))
                    .cornerRadius(8)
                    .frame(maxWidth: .infinity)
                }
            } else {
                // Field picker Menu
                Menu {
                    ForEach(dataManager.fields) { field in
                        Button {
                            dataManager.selectedFieldID = field.id
                        } label: {
                            HStack {
                                Text(field.name)
                                    .foregroundColor(Color("TextColor"))
                                if dataManager.selectedFieldID == field.id {
                                    Spacer()
                                    Image(systemName: "checkmark")
                                        .foregroundColor(Color("ThemePrimaryColor"))
                                }
                            }
                        }
                    }

                    Divider()
                        .overlay(Color("BorderColor"))

                    Button {
                        showingAddFieldSheet = true
                    } label: {
                        Label("Add New Field", systemImage: "plus")
                    }
                } label: {
                    HStack {
                        Text(dataManager.selectedField?.name ?? "Select a Field")
                            .font(.headline)
                            .foregroundColor(dataManager.selectedField == nil ? Color("TextLightColor") : Color("TextColor"))

                        Spacer()

                        if let field = dataManager.selectedField {
                            Text("\(field.geoPoints.count) points")
                                .font(.caption)
                                .foregroundColor(Color("TextLightColor"))
                                .padding(.trailing, 4)
                        }

                        Image(systemName: "chevron.down.circle.fill")
                            .foregroundColor(Color("AccentColor"))
                            .font(.title3)
                    }
                    .padding(12)
                    .background(Color("CardBackgroundColor"))
                    .cornerRadius(8)
                    .frame(maxWidth: .infinity)
                }
            }
        }
        .sheet(isPresented: $showingAddFieldSheet) {
            AddFieldView(fieldName: $newFieldName) {
                let nameToSave = newFieldName.trimmingCharacters(in: .whitespaces)
                guard !nameToSave.isEmpty else { return }
                let newField = dataManager.createField(name: nameToSave)
                dataManager.selectedFieldID = newField.id
                newFieldName = ""
            }
            .environmentObject(dataManager)
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        FieldSelectorView()
            .padding(.horizontal)

        FieldSelectorView(showLabel: false)
            .padding(.horizontal)
    }
    .environmentObject(DataManager())
    .padding(.vertical)
}
