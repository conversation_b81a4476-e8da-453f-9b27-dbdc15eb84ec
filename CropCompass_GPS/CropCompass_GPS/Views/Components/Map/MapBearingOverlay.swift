import SwiftUI

/// An overlay view that displays the current map heading/bearing and its cardinal direction at the top.
struct MapBearingOverlay: View {
    var mapHeading: Double // Current heading of the map in degrees

    var body: some View {
        HStack(spacing: 5) { // Keep HStack for potential future icons, or just use Text directly
            // Removed the rotating arrow Image

            Text("Bearing: \(formattedHeading)° (\(cardinalDirection(for: mapHeading)))") // Add cardinal direction
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(Color("TextColor")) // Use theme text color
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 5)
        .background(
            Capsule()
                .fill(Color("CardBackgroundColor").opacity(0.9)) // Use CardBackgroundColor instead of material
        )
        // Removed shadow for flatter theme
        // .shadow(radius: 3)
        .padding(.top, 8)
    }

    /// Format the heading to an integer (0-359).
    private var formattedHeading: Int {
        let normalizedHeading = mapHeading.truncatingRemainder(dividingBy: 360)
        let positiveHeading = normalizedHeading < 0 ? normalizedHeading + 360 : normalizedHeading
        // Ensure result is 0-359, handle edge case of exactly 360
        let finalHeading = positiveHeading == 360 ? 0 : positiveHeading
        return Int(round(finalHeading))
    }

    /// Determine the cardinal/intercardinal direction for a given bearing.
    private func cardinalDirection(for bearing: Double) -> String {
        let directions = ["N", "NE", "E", "SE", "S", "SW", "W", "NW"]
        // Normalize bearing to 0-359
        let normalizedBearing = bearing.truncatingRemainder(dividingBy: 360)
        let positiveBearing = normalizedBearing < 0 ? normalizedBearing + 360 : normalizedBearing
        // Calculate index: each segment is 360/8 = 45 degrees. Shift by 22.5 degrees
        // so that N is centered around 0, NE around 45, etc.
        // Use floor instead of round to handle boundaries correctly (e.g., 0 should be N)
        let index = Int(floor((positiveBearing + 22.5) / 45.0)) % 8
        return directions[index]
    }
}

#Preview {
    VStack(spacing: 15) {
        // Test cardinal directions
        MapBearingOverlay(mapHeading: 0)    // N
        MapBearingOverlay(mapHeading: 20)   // N
        MapBearingOverlay(mapHeading: 45)   // NE
        MapBearingOverlay(mapHeading: 70)   // NE
        MapBearingOverlay(mapHeading: 90)   // E
        MapBearingOverlay(mapHeading: 120)  // E
        MapBearingOverlay(mapHeading: 135)  // SE
        MapBearingOverlay(mapHeading: 160)  // SE
        MapBearingOverlay(mapHeading: 180)  // S
        MapBearingOverlay(mapHeading: 210)  // S
        MapBearingOverlay(mapHeading: 225)  // SW
        MapBearingOverlay(mapHeading: 250)  // SW
        MapBearingOverlay(mapHeading: 270)  // W
        MapBearingOverlay(mapHeading: 300)  // W
        MapBearingOverlay(mapHeading: 315)  // NW
        MapBearingOverlay(mapHeading: 340)  // NW
        MapBearingOverlay(mapHeading: 359)  // N
        MapBearingOverlay(mapHeading: -20)  // NW (340)
    }
    .padding()
    .background(Color.gray.opacity(0.2))
}