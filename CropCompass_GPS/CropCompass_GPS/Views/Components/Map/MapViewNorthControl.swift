import SwiftUI
import MapKit

// A simple view that helps control the map's orientation
struct MapViewNorthControl: View {
    var setNorth: Bo<PERSON>
    @Binding var cameraPosition: MapCameraPosition
    var visibleRegion: MKCoordinateRegion?

    var body: some View {
        // Empty view that uses onAppear and onChange to control the map
        Color.clear
            .frame(width: 0, height: 0)
            .onChange(of: setNorth) { oldValue, newValue in
                if newValue == true {
                    orientMapToNorth()
                }
            }
    }

    // Function to orient the map to North without changing zoom
    private func orientMapToNorth() {
        guard let region = visibleRegion else { return }

        // Use a custom approach to maintain zoom while setting heading to North
        // First, create a region with the same center and span
        let updatedRegion = MKCoordinateRegion(
            center: region.center,
            span: region.span
        )

        // First set the region to maintain the zoom level
        cameraPosition = .region(updatedRegion)

        // Then set the heading to 0 (North) with a slight delay
        // This two-step approach helps maintain the zoom level
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // Create a camera with the same center but heading set to 0
            let camera = MapCamera(
                centerCoordinate: region.center,
                distance: 5000,  // Use a value that won't affect the zoom much
                heading: 0,      // North orientation
                pitch: 0         // No tilt
            )

            // Update the camera position
            cameraPosition = .camera(camera)
        }
    }
}
