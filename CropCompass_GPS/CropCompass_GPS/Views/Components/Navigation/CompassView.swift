import SwiftUI
import CoreLocation

struct CompassView: View {
    @EnvironmentObject var locationManager: LocationManager
    @State private var showCustomPointSheet = false
    @State private var customHeading: Double = 0
    @State private var useCustomHeading = false

    // Computed property to get the current heading
    private var currentHeading: Double {
        locationManager.heading?.trueHeading ?? 0
    }

    // Format heading for display
    private var formattedHeading: String {
        return String(format: "%d°", Int(currentHeading))
    }

    // Determine which direction to turn to reach north or custom point
    private var turnDirection: TurnDirection {
        let targetHeading = useCustomHeading ? customHeading : 0 // North is 0 degrees
        let diff = (targetHeading - currentHeading + 360).truncatingRemainder(dividingBy: 360)

        if diff < 5 || diff > 355 { // Within 5 degrees of target
            return .onTarget
        } else if diff <= 180 {
            return .right
        } else {
            return .left
        }
    }

    // Determine chevron colors based on current heading and target direction (Hardcoded Blue/Green)
    private var leftChevronColor: Color {
        let targetHeading = useCustomHeading ? customHeading : 0 // North is 0 degrees
        let diff = (targetHeading - currentHeading + 360).truncatingRemainder(dividingBy: 360)

        // When pointing at target direction (or close), both chevrons are green
        if diff < 5 || diff > 355 {
            return Color.green
        }
        // When pointing opposite to target direction, both chevrons are blue (less emphasis)
        else if diff > 175 && diff < 185 {
            return Color.blue
        }
        // When target is to the left (counterclockwise), left chevron is green (indicates turn left)
        else if diff > 180 {
            return Color.green
        }
        // When target is to the right (clockwise), left chevron is blue
        else {
            return Color.blue
        }
    }

    private var rightChevronColor: Color {
        let targetHeading = useCustomHeading ? customHeading : 0
        let diff = (targetHeading - currentHeading + 360).truncatingRemainder(dividingBy: 360)

        // When pointing at target direction (or close), both chevrons are green
        if diff < 5 || diff > 355 {
            return Color.green
        }
        // When pointing opposite to target direction, both chevrons are blue (less emphasis)
        else if diff > 175 && diff < 185 {
            return Color.blue
        }
        // When target is to the right (clockwise), right chevron is green (indicates turn right)
        else if diff <= 180 {
            return Color.green
        }
        // When target is to the left (counterclockwise), right chevron is blue
        else {
            return Color.blue
        }
    }

    // Constants for the tape measure style compass
    private let compassHeight: CGFloat = 70
    private let compassWidth: CGFloat = UIScreen.main.bounds.width - 40 // Full width minus padding
    private let tickHeight: CGFloat = 15
    private let majorTickSpacing: CGFloat = 30 // Degrees between major ticks
    private let pixelsPerDegree: CGFloat = 3 // How many pixels per degree

    var body: some View {
        VStack(spacing: 4) {
            // Tape measure style compass with direction indicators
            ZStack(alignment: .center) {
                // Background
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color("CardBackgroundColor"))
                    .frame(width: compassWidth, height: compassHeight)

                // Degree marks and labels
                CompassTape(currentHeading: currentHeading, customHeading: useCustomHeading ? customHeading : nil)
                    .frame(width: compassWidth, height: compassHeight)
                    .clipShape(RoundedRectangle(cornerRadius: 8))

                // Center indicator
                VStack(spacing: 0) {
                    Rectangle()
                        .fill(Color("ErrorColor"))
                        .frame(width: 2, height: compassHeight - 14)

                    Image(systemName: "arrowtriangle.up.fill")
                        .foregroundColor(Color("ErrorColor"))
                        .font(.system(size: 14))
                }
            }
            .onTapGesture {
                showCustomPointSheet = true
            }
            .sheet(isPresented: $showCustomPointSheet) {
                CustomHeadingSheet(customHeading: $customHeading, useCustomHeading: $useCustomHeading)
            }

            // Current heading display with chevrons
            HStack(spacing: 15) {
                // Left chevron (outward-pointing)
                Image(systemName: "chevron.backward.2")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(leftChevronColor)

                // Heading display
                Text(formattedHeading)
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(.primary)
                    .frame(height: 25)

                // Right chevron (outward-pointing)
                Image(systemName: "chevron.forward.2")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(rightChevronColor)
            }
            .padding(.top, 2)
        }
        .padding(.top, 8)
        .accessibilityElement(children: .ignore)
        .accessibilityLabel("Compass showing \(formattedHeading)")
        .accessibilityIdentifier("view.compass")
        .onAppear {
            // Start updating heading when the view appears
            locationManager.startUpdatingHeading()
        }
        .onDisappear {
            // Stop updating heading when the view disappears
            locationManager.stopUpdatingHeading()
        }
    }
}

// Direction to turn
enum TurnDirection {
    case left
    case right
    case onTarget
}

// Sheet for setting custom heading
struct CustomHeadingSheet: View {
    @Binding var customHeading: Double
    @Binding var useCustomHeading: Bool
    @Environment(\.dismiss) private var dismiss
    // State for text input & edit mode
    @State private var headingInputString: String = ""
    @State private var isEditingHeading: Bool = false
    @FocusState private var headingFieldIsFocused: Bool

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Custom Direction")) {
                    Toggle("Use Custom Direction", isOn: $useCustomHeading)

                    if useCustomHeading {
                        VStack {
                            // Display Area: Switches between Text and TextField
                            if isEditingHeading {
                                TextField("Heading", text: $headingInputString)
                                    .keyboardType(.numberPad)
                                    .multilineTextAlignment(.center)
                                    .font(.title) // Use title font for editing
                                    .padding(.vertical, 5)
                                    .background(RoundedRectangle(cornerRadius: 5).fill(Color(uiColor: .secondarySystemBackground)))
                                    .focused($headingFieldIsFocused)
                                    .onSubmit {
                                        validateAndUpdateHeading(from: headingInputString)
                                        isEditingHeading = false // Exit edit mode on submit
                                    }
                                    // Add toolbar for keyboard dismissal if needed
                                    .toolbar {
                                        ToolbarItemGroup(placement: .keyboard) {
                                            Spacer()
                                            Button("Done") {
                                                headingFieldIsFocused = false
                                                validateAndUpdateHeading(from: headingInputString)
                                                isEditingHeading = false
                                            }
                                        }
                                    }

                            } else {
                                Text("\(Int(customHeading))°") // Display integer value with degree symbol
                                    .font(.title)
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding(.vertical, 5) // Match TextField padding roughly
                                    .onTapGesture {
                                        // Prepare string for editing (without degree symbol)
                                        headingInputString = String(format: "%.0f", customHeading)
                                        isEditingHeading = true
                                    }
                            }

                            // Slider for Visual Adjustment
                            Slider(value: $customHeading, in: 0...359, step: 1)
                                .padding(.horizontal)
                                // Synchronize Slider changes TO the TextField state (even if not visible)
                                .onChange(of: customHeading) { _, newValue in
                                    headingInputString = String(format: "%.0f", newValue)
                                    // If user is editing text while sliding, stop editing to prevent conflict
                                    if isEditingHeading {
                                        isEditingHeading = false
                                    }
                                }
                        }
                        .frame(maxWidth: .infinity) // Center VStack content
                    }
                }

                Section {
                    Button("Set to North (0°)") {
                        customHeading = 0
                        useCustomHeading = true
                    }

                    Button("Set to East (90°)") {
                        customHeading = 90
                        useCustomHeading = true
                    }

                    Button("Set to South (180°)") {
                        customHeading = 180
                        useCustomHeading = true
                    }

                    Button("Set to West (270°)") {
                        customHeading = 270
                        useCustomHeading = true
                    }
                }
            }
            .navigationTitle("Compass Settings")
            .navigationBarItems(trailing: Button("Done") {
                // If user taps Done while editing, save the value first
                if isEditingHeading {
                     validateAndUpdateHeading(from: headingInputString)
                     isEditingHeading = false
                }
                dismiss()
            })
        }
        // Initialize text field state variable when the sheet appears
        .onAppear {
            headingInputString = String(format: "%.0f", customHeading)
        }
        // Manage focus when isEditingHeading changes
        .onChange(of: isEditingHeading) { _, editing in
            headingFieldIsFocused = editing
        }
    }

    // Helper function to validate text input and update the double binding
    private func validateAndUpdateHeading(from stringValue: String) {
        if let value = Int(stringValue), (0...359).contains(value) {
            customHeading = Double(value)
            // Update string state in case input had extra stuff (e.g., spaces)
            headingInputString = String(value)
        } else {
            // Handle invalid input: Reset the string to the last valid customHeading value
             headingInputString = String(format: "%.0f", customHeading)
             print("Invalid heading input: \(stringValue). Resetting to \(headingInputString)")
             // Optionally provide haptic feedback for error
             // UINotificationFeedbackGenerator().notificationOccurred(.error)
        }
    }
}

// Compass tape component that shows the degree markings
struct CompassTape: View {
    var currentHeading: Double
    var customHeading: Double?

    // Constants
    private let pixelsPerDegree: CGFloat = 3
    private let majorTickSpacing: Int = 30 // Degrees between major ticks
    private let minorTickSpacing: Int = 10 // Degrees between minor ticks
    private let compassRange: Int = 360 // Total degrees to show

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                Color("CardBackgroundColor")

                // Custom direction marker (blue line)
                if let customHeading = customHeading {
                    let diff = (customHeading - currentHeading + 360).truncatingRemainder(dividingBy: 360)
                    let adjustedDiff = diff > 180 ? diff - 360 : diff
                    let offset = CGFloat(adjustedDiff) * pixelsPerDegree

                    Rectangle()
                        .fill(Color("ThemePrimaryColor"))
                        .frame(width: 2, height: geometry.size.height * 0.7)
                        .position(x: geometry.size.width/2 + offset, y: geometry.size.height/2)
                        .zIndex(1) // Ensure it's drawn on top
                }

                // Draw the degree markings
                ForEach(-180...180, id: \.self) { degree in
                    let adjustedDegree = (Int(currentHeading) + degree + 360) % 360
                    let isMajorTick = adjustedDegree % majorTickSpacing == 0
                    let isMinorTick = adjustedDegree % minorTickSpacing == 0

                    if isMajorTick || isMinorTick {
                        VStack(spacing: 2) {
                            // Tick mark
                            Rectangle()
                                .fill(Color.primary)
                                .frame(width: isMajorTick ? 2 : 1,
                                       height: isMajorTick ? 15 : 8)

                            // Degree label for major ticks
                            if isMajorTick {
                                Text("\(adjustedDegree)°")
                                    .font(.system(size: 10))
                                    .foregroundColor(.primary)

                                // Cardinal direction labels below the degrees
                                if adjustedDegree == 0 {
                                    Text("N")
                                        .font(.system(size: 12, weight: .bold))
                                        .foregroundColor(Color("ErrorColor"))
                                        .offset(y: 12)
                                } else if adjustedDegree == 90 {
                                    Text("E")
                                        .font(.system(size: 12, weight: .bold))
                                        .foregroundColor(.primary)
                                        .offset(y: 12)
                                } else if adjustedDegree == 180 {
                                    Text("S")
                                        .font(.system(size: 12, weight: .bold))
                                        .foregroundColor(.primary)
                                        .offset(y: 12)
                                } else if adjustedDegree == 270 {
                                    Text("W")
                                        .font(.system(size: 12, weight: .bold))
                                        .foregroundColor(.primary)
                                        .offset(y: 12)
                                }
                            }
                        }
                        .position(x: geometry.size.width/2 + CGFloat(degree) * pixelsPerDegree,
                                 y: isMajorTick ? 25 : 20)
                    }
                }
            }
        }
    }
}

#Preview {
    CompassView()
        .environmentObject(LocationManager(dataManager: DataManager()))
}
