import SwiftUI

struct FieldManagementView: View {
    @EnvironmentObject var dataManager: DataManager
    @State private var newFieldName: String = ""
    @State private var showingAddField = false
    @Environment(\.dismiss) private var dismiss

    // State for Renaming
    @State private var showingRenameFieldAlert = false
    @State private var showingDeleteFieldAlert = false
    @State private var itemToRenameId: UUID? = nil
    @State private var itemToDeleteId: UUID? = nil
    @State private var itemToRenameCurrentName: String = ""
    @State private var renameText: String = ""

    var body: some View {
        NavigationView {
            List {
                Section(header: Text("Fields").foregroundColor(Color("TextLightColor"))) {
                    ForEach(dataManager.fields) { field in
                        HStack {
                            // Rename Field Button
                            Button {
                                itemToRenameId = field.id
                                itemToRenameCurrentName = field.name
                                renameText = field.name
                                showingRenameFieldAlert = true
                            } label: {
                                Image(systemName: "pencil")
                                    .foregroundColor(Color("AccentColor")) // Use AccentColor
                            }
                            .buttonStyle(.borderless)
                            .padding(.trailing, 8)

                            // Field Name and Point Count
                            VStack(alignment: .leading) {
                                Text(field.name)
                                    .foregroundColor(Color("TextColor")) // Theme text
                                    .accessibilityIdentifier("label.field.name")

                                Text("\(field.geoPoints.count) points")
                                    .font(.caption)
                                    .foregroundColor(Color("TextLightColor")) // Theme light text
                            }

                            Spacer()

                            // Delete Field Button
                            Button {
                                itemToDeleteId = field.id
                                showingDeleteFieldAlert = true
                            } label: {
                                Image(systemName: "trash")
                                    .foregroundColor(Color("ErrorColor")) // Use ErrorColor
                            }
                            .buttonStyle(.borderless)
                        }
                        .accessibilityIdentifier("field.row")
                        .contentShape(Rectangle())
                        // Optional: Theme list row background
                        // .listRowBackground(Color("CardBackgroundColor"))
                    }

                    // Add New Field Button
                    Button(action: {
                        showingAddField = true
                    }) {
                        Label("Add New Field", systemImage: "plus.circle")
                            .foregroundColor(Color("ThemePrimaryColor")) // Use primary color
                    }
                    .accessibilityIdentifier("button.field.add")
                }
            }
            .background(Color("BackgroundColor").ignoresSafeArea()) // Explicitly set background
            .navigationTitle("Manage Fields")
            .accessibilityIdentifier("view.fields")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    // Theme Done button
                    Button("Done") {
                        dismiss()
                    }
                    .foregroundColor(Color("ThemePrimaryColor")) // Theme color
                    .fontWeight(.semibold) // Optional: Make slightly bolder
                    .accessibilityIdentifier("button.done")
                }
            }

            .sheet(isPresented: $showingAddField) {
                AddFieldView(fieldName: $newFieldName) {
                    if !newFieldName.isEmpty {
                        _ = dataManager.createField(name: newFieldName)
                        newFieldName = ""
                    }
                }
                .accessibilityIdentifier("sheet.field")
            }

            // Alert for Renaming Field
             .alert("Rename Field", isPresented: $showingRenameFieldAlert, presenting: itemToRenameId) { _ in
                 TextField("New Field Name", text: $renameText)
                     .autocapitalization(.words)
                 Button("Rename") {
                     let trimmedName = renameText.trimmingCharacters(in: .whitespaces)
                     if let fieldId = itemToRenameId, !trimmedName.isEmpty, trimmedName != itemToRenameCurrentName {
                         dataManager.updateFieldName(fieldId: fieldId, newName: trimmedName)
                     }
                     resetRenameState()
                 }
                 Button("Cancel", role: .cancel) {
                     resetRenameState()
                 }
             } message: { _ in
                 Text("Enter a new name for \"\(itemToRenameCurrentName)\".")
             }

             // Alert for Deleting Field
             .alert("Delete Field?", isPresented: $showingDeleteFieldAlert) {
                 Button("Cancel", role: .cancel) {
                     itemToDeleteId = nil
                 }
                 Button("Delete", role: .destructive) {
                     if let fieldId = itemToDeleteId {
                         _ = dataManager.deleteField(fieldId: fieldId)
                     }
                     itemToDeleteId = nil
                 }
             } message: {
                 if let fieldId = itemToDeleteId, let field = dataManager.fields.first(where: { $0.id == fieldId }) {
                     Text("Are you sure you want to delete '\(field.name)'? This will delete all recorded points within this field.")
                 } else {
                     Text("Are you sure you want to delete this field? This will delete all recorded points within this field.")
                 }
             }
        }
        .accessibilityIdentifier("sheet.field.management")
    }

    // Helper to reset renaming state
    private func resetRenameState() {
        itemToRenameId = nil
        itemToRenameCurrentName = ""
        renameText = ""
        showingRenameFieldAlert = false
    }
}

#Preview {
    FieldManagementView()
        .environmentObject(DataManager())
}
