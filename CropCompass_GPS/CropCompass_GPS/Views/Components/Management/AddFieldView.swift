import SwiftUI

struct AddFieldView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var fieldName: String
    var onSave: () -> Void

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Field Details").foregroundColor(Color("TextLightColor"))) {
                    TextField("Field Name", text: $fieldName)
                        .accessibilityIdentifier("input.field.name")
                        .foregroundColor(Color("TextColor"))
                }
            }
            .background(Color("BackgroundColor").ignoresSafeArea())
            .navigationTitle("Add New Field")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(Color("AccentColor"))
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        onSave()
                        dismiss()
                    }
                    .foregroundColor(Color("ThemePrimaryColor"))
                    .fontWeight(.semibold)
                    .accessibilityIdentifier("button.field.save")
                    .disabled(fieldName.isEmpty)
                }
            }
        }
        .accessibilityIdentifier("view.addfield")
    }
}

struct AddFieldPreview: View {
    @State private var fieldName = ""

    var body: some View {
        AddFieldView(fieldName: $fieldName) {
            print("Field saved: \(fieldName)")
        }
    }
}

#Preview {
    AddFieldPreview()
}
