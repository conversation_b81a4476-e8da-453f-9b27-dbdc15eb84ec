import SwiftUI
import CoreLocation
import UIKit

// Tag Constants are now defined globally in TagConstants.swift

struct MarkSpotNoteView: View {
    // Environment and passed properties
    @Environment(\.dismiss) var dismiss // To close the sheet
    @EnvironmentObject var dataManager: DataManager // Inject DataManager
    @AppStorage("measurementSystem") private var measurementSystem: String = MeasurementSystem.metric.rawValue
    let location: CLLocation
    let fieldID: UUID
    let onSave: (String?, PointType) -> Void // Callback to save the point with the note and type

    // State for user input
    @State private var selectedTag: String = noTag // Default to No Tag - Uses global constant
    @State private var freeTextNote: String = ""    // Renamed from noteText, always available
    @State private var showingSuccessMessage = false

    // Computed property for available tags - always use regularTags
    private var availableTags: [String] {
        regularTags // Always use tags for .marked points
    }

    var body: some View {
        // Get tags and field name
        let currentTagsForPicker: [String] = availableTags
        let fieldName = dataManager.fields.first { $0.id == fieldID }?.name ?? "Unknown Field"

        NavigationView { // Embed in NavigationView for title and toolbar
            ScrollView {
                VStack(spacing: 16) {
                    locationSection
                    tagSection(currentTagsForPicker: currentTagsForPicker)
                    notesSection

                    Spacer(minLength: 60)
                }
                .padding()
            }
            .background(Color("BackgroundColor")) // Theme background
            .scrollDismissesKeyboard(.interactively)
            .navigationTitle("Mark Spot") // Title is always Mark Spot now
            .navigationBarTitleDisplayMode(.inline)
            .safeAreaInset(edge: .top) { // Display Field Name below title
                Text("Field: \(fieldName)")
                    .font(.subheadline)
                    .foregroundColor(Color("TextLightColor")) // Theme secondary text
                    .padding(.bottom, 2)
                    .frame(maxWidth: .infinity)
                    .background(Color("BackgroundColor"))
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(Color("AccentColor"))
                    .accessibilityIdentifier("button.cancel")
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveAction()
                    }
                    .foregroundColor(Color("ThemePrimaryColor"))
                    .fontWeight(.semibold)
                    .accessibilityIdentifier("button.savepoint")
                }
            }
            .overlay(
                Group {
                    if showingSuccessMessage {
                        Text("Point added successfully!")
                            .padding()
                            .background(Material.thin)
                            .foregroundColor(Color("SuccessColor"))
                            .cornerRadius(10)
                            .transition(.opacity.animation(.easeInOut))
                            .accessibilityIdentifier("label.pointadded")
                    }
                }
            )
        }
        .accessibilityIdentifier("view.markspot")
        .onAppear(perform: setupInitialTag)
    }

    // MARK: - Computed View Properties for Sections

    private var locationSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Location")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
                .padding(.bottom, 4)
            VStack(spacing: 10) { 
                HStack {
                    LabelledLocationValueView(
                        label: "Latitude",
                        value: String(format: "%.6f°", location.coordinate.latitude),
                        alignment: .leading,
                        valueFont: .title3,
                        valueWeight: .bold
                    )
                    Spacer()
                    LabelledLocationValueView(
                        label: "Longitude",
                        value: String(format: "%.6f°", location.coordinate.longitude),
                        alignment: .trailing,
                        valueFont: .title3,
                        valueWeight: .bold
                    )
                }
                HStack {
                    LabelledLocationValueView(
                        label: "Elevation",
                        value: formatElevation(location.altitude),
                        alignment: .leading
                    )
                    Spacer()
                    LabelledLocationValueView(
                        label: "Accuracy",
                        value: formatDistance(location.horizontalAccuracy),
                        alignment: .trailing,
                        valueColor: Color.accuracyColor(for: location.horizontalAccuracy)
                    )
                }
            }
            .accessibilityIdentifier("label.locationdetails")
        }
        .padding()
        .background(Color("BackgroundColor"))
    }

    private func tagSection(currentTagsForPicker: [String]) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Tag")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
                .padding(.bottom, 4)
            Menu {
                ForEach(currentTagsForPicker, id: \.self) { tag in
                    Button(tag) { 
                        selectedTag = tag
                    }
                }
            } label: {
                Text(selectedTag)
                    .foregroundColor(Color("TextColor"))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(10)
            .background(Color("BackgroundColor"))
            .cornerRadius(8)
            .accessibilityIdentifier("menu.tag")
        }
        .padding()
        .background(Color("BackgroundColor"))
    }

    private var notesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Notes")
                .font(.headline)
                .foregroundColor(Color("TextColor"))
                .padding(.bottom, 4)
            TextField("Add optional details...", text: $freeTextNote, axis: .vertical)
                .lineLimit(3...5)
                .textFieldStyle(.plain)
                .foregroundColor(Color("TextColor"))
                .frame(minHeight: 100)
                .padding(.vertical, 8)
                .accessibilityIdentifier("input.freetextnote")
                .overlay(alignment: .trailing) {
                    if freeTextNote.isEmpty == false {
                        Button(action: {
                            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                        }) {
                            Image(systemName: "keyboard.chevron.compact.down")
                                .foregroundColor(.blue)
                                .padding(8)
                        }
                        .padding(.trailing, 4)
                    }
                }
                .onChange(of: freeTextNote) { oldValue, newValue in
                    if oldValue.isEmpty && !newValue.isEmpty && selectedTag == noTag {
                        selectedTag = customTag
                    }
                }
                .padding(10)
                .background(Color("BackgroundColor"))
                .cornerRadius(8)
        }
        .padding()
        .background(Color("BackgroundColor"))
    }

    // MARK: - Helper Methods
    
    private func formatElevation(_ elevation: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        if system == .metric {
            return String(format: "%.2f m", elevation)
        } else {
            let feet = elevation * 3.28084
            return String(format: "%.2f ft", feet)
        }
    }
    
    private func formatDistance(_ distance: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        if system == .metric {
            return String(format: "%.1f m", distance)
        } else {
            let feet = distance * 3.28084
            return String(format: "%.1f ft", feet)
        }
    }

    private func setupInitialTag() {
        // Use regularTags since type is fixed to .marked
        if !regularTags.contains(selectedTag) {
            selectedTag = noTag
        }
    }

    private func saveAction() {
        let tagPart: String?
        if selectedTag == noTag || selectedTag == customTag {
            tagPart = nil
        } else {
            tagPart = selectedTag
        }

        let notePart = freeTextNote.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalNote: String?

        if let tag = tagPart, !notePart.isEmpty {
            finalNote = "\(tag) - \(notePart)"
        } else if let tag = tagPart {
            finalNote = tag
        } else if !notePart.isEmpty {
            finalNote = notePart
        } else {
            finalNote = nil
        }

        // Always save as .marked type
        onSave(finalNote, .marked)
        showingSuccessMessage = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            showingSuccessMessage = false
            dismiss()
        }
    }
}

#Preview {
    let previewLocation = CLLocation(latitude: 51.5074, longitude: -0.1278)
    let previewFieldID = UUID()

    MarkSpotNoteView(location: previewLocation, fieldID: previewFieldID) { note, pointType in
        print("Preview Save Tapped. Note: \(note ?? "nil"), Type: \(pointType)") // Type will always be .marked now
    }
    .environmentObject(DataManager())
}
