import SwiftUI

// MARK: - Reusable Helper View for Location Details

struct LabelledLocationValueView: View {
    let label: String
    let value: String
    var alignment: HorizontalAlignment = .leading
    var valueFont: Font = .body
    var valueWeight: Font.Weight = .regular
    var valueColor: Color? // Allow optional override color

    var body: some View {
        VStack(alignment: alignment, spacing: 2) {
            Text(label)
                .font(.caption)
                .foregroundColor(Color("TextLightColor")) // Use theme light text for label
            
            Text(value)
                .font(valueFont)
                .fontWeight(valueWeight)
                // Use passed-in color OR default theme text color
                .foregroundColor(valueColor ?? Color("TextColor")) 
        }
    }
}

// Optional: Add a preview for the helper view itself
#Preview("Leading") {
    LabelledLocationValueView(label: "Latitude", value: "51.5074°", alignment: .leading, valueFont: .title3, valueWeight: .bold)
}

#Preview("Trailing Red") {
    LabelledLocationValueView(label: "Accuracy", value: "12.3 m", alignment: .trailing, valueColor: .red)
}
