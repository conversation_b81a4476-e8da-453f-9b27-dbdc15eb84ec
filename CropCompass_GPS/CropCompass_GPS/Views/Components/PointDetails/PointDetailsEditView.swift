import SwiftUI
import CoreLocation // Needed for coordinate display

// Tag Constants are now defined globally in TagConstants.swift

// Point Details Edit View (Moved to Components/PointDetails)
struct PointDetailsEditView: View {
    @EnvironmentObject var dataManager: DataManager
    @Environment(\.dismiss) private var dismiss
    @AppStorage("measurementSystem") private var measurementSystem: String = MeasurementSystem.metric.rawValue

    let point: GeoPoint

    // State variables
    @State private var editedType: PointType
    @State private var editedTag: String
    @State private var editedNote: String
    // State for delete confirmation
    @State private var showingDeleteConfirmation = false

    // Computed property for available tags based on point type - Uses global constants
    private var availableTags: [String] {
        editedType == .marked ? regularTags : boundaryTags
    }

    init(point: GeoPoint) {
        self.point = point

        // Initialise state from the GeoPoint
        _editedType = State(initialValue: point.pointType)

        // Attempt to parse the note into tag and free text - Uses global constants
        let (initialTag, initialNote) = Self.parseNote(point.note, for: point.pointType)
        _editedTag = State(initialValue: initialTag)
        _editedNote = State(initialValue: initialNote)
    }

    var body: some View {
        // Apply theme background to NavigationView
        NavigationView {
            ScrollView {
                VStack(spacing: 16) {
                    pointTypeSection // Always show type section

                    if editedType == .marked {
                        tagSection       // Use computed property for section
                        notesSection     // Use computed property for section
                    }

                    PointDetailsReadOnlySection(point: point, measurementSystem: measurementSystem) // Always show read-only details
                    Spacer(minLength: 60)
                }
                .padding()
            }
            .background(Color("BackgroundColor")) // Set ScrollView background
            .scrollDismissesKeyboard(.interactively)
            .navigationTitle("Edit Point")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    // Use AccentColor for Cancel
                    Button("Cancel") { dismiss() }
                        .foregroundColor(Color("AccentColor"))
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    // Use ThemePrimaryColor for Save
                    Button("Save") { saveChanges() }
                        .foregroundColor(Color("ThemePrimaryColor"))
                        .fontWeight(.semibold) // Make Save slightly bolder
                }
                // Add Delete button to bottom bar for emphasis
                ToolbarItem(placement: .bottomBar) {
                    Button(role: .destructive) {
                        showingDeleteConfirmation = true
                    } label: {
                        Label("Delete Point", systemImage: "trash")
                            .foregroundColor(.red) // Explicitly set red color
                    }
                    .frame(maxWidth: .infinity, alignment: .center) // Center the button
                }
            }
            // Ensure Navigation Bar background adapts (usually automatic)
            .alert("Delete Point?", isPresented: $showingDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    if dataManager.deleteGeoPoint(pointId: point.id) {
                        print("Point \(point.id) deleted successfully.")
                        dismiss() // Dismiss the sheet after deletion
                    } else {
                        print("Failed to delete point \(point.id).")
                        // Optionally show another alert for failure?
                    }
                }
            } message: {
                Text("Are you sure you want to permanently delete this point? This action cannot be undone.")
            }
        }
        // Apply theme text color potentially to NavigationView if needed
        // .foregroundColor(Color("TextColor"))
    }

    // MARK: - Computed View Properties for Sections

    private var pointTypeSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Point Type")
                .font(.headline)
                .foregroundColor(Color("TextColor")) // Theme text
                .padding(.bottom, 4)

            Picker("Point Type", selection: $editedType) {
                Text("Boundary").tag(PointType.boundary)
                    .foregroundColor(Color("TextColor")) // Theme text
                Text("Marked").tag(PointType.marked)
                    .foregroundColor(Color("TextColor")) // Theme text
                Text("Track").tag(PointType.continuous)
                    .foregroundColor(Color("TextColor")) // Theme text
            }
            .pickerStyle(.segmented)
            // Tint the selected segment
            .colorMultiply(Color("ThemePrimaryColor"))
            .padding(.vertical, 5)
            .accessibilityIdentifier("picker.pointtype.edit")
            .onChange(of: editedType) { _, newType in
                // Reset tag if current one isn't valid for the new type - Uses global constants
                let relevantTags = newType == .marked ? regularTags : boundaryTags
                if !relevantTags.contains(editedTag) {
                    editedTag = noTag
                }
            }
        }
        .padding()
        .background(Color("CardBackgroundColor")) // Use card background
        .cornerRadius(10)
        // Removed shadow modifier for flatter theme
        // .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }

    private var tagSection: some View {
         VStack(alignment: .leading, spacing: 8) {
            Text("Tag")
                .font(.headline)
                .foregroundColor(Color("TextColor")) // Theme text
                .padding(.bottom, 4)
            Menu {
                ForEach(availableTags, id: \.self) { tag in
                    Button(tag) { editedTag = tag }
                        // Menu items use system styling, usually okay
                }
            } label: {
                Text(editedTag)
                    .foregroundColor(Color("TextColor")) // Theme text
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(10)
            .background(Color("BackgroundLightColor")) // Use lighter background for input
            .cornerRadius(8)
            .accessibilityIdentifier("menu.tag.edit")
        }
        .padding()
        .background(Color("CardBackgroundColor")) // Use card background
        .cornerRadius(10)
        // Removed shadow
    }

    private var notesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Notes")
                .font(.headline)
                .foregroundColor(Color("TextColor")) // Theme text
                .padding(.bottom, 4)
            TextField("Add optional details...", text: $editedNote, axis: .vertical)
                .lineLimit(3...5)
                .textFieldStyle(.plain)
                .foregroundColor(Color("TextColor")) // Theme text
                .frame(minHeight: 80)
                .padding(10)
                .background(Color("BackgroundLightColor")) // Use lighter background for input
                .cornerRadius(8)
                .accessibilityIdentifier("input.freetextnote.edit")
                 // Automatically set tag to custom if user types note without selecting a tag first - Uses global constants
                .onChange(of: editedNote) { oldValue, newValue in
                    if oldValue.isEmpty && !newValue.isEmpty && editedTag == noTag {
                        editedTag = customTag
                    }
                }
        }
        .padding()
        .background(Color("CardBackgroundColor")) // Use card background
        .cornerRadius(10)
        // Removed shadow
    }

    // MARK: - Helper Methods

    // Static method to parse the combined note string - Uses global constants
    private static func parseNote(_ note: String?, for type: PointType) -> (tag: String, note: String) {
        guard let note = note, !note.isEmpty else {
            return (noTag, "")
        }

        let relevantTags = type == .marked ? regularTags : boundaryTags
        let tagsToCheck = relevantTags.filter { $0 != noTag && $0 != customTag } // Exclude special tags

        // Case 1: Note contains " - " separator
        if let separatorRange = note.range(of: " - ") {
            let potentialTag = String(note[..<separatorRange.lowerBound])
            let remainingNote = String(note[separatorRange.upperBound...])

            if tagsToCheck.contains(potentialTag) {
                return (potentialTag, remainingNote)
            } else {
                 // Separator present but first part isn't a known tag
                return (customTag, note)
            }
        }

        // Case 2: Note exactly matches a known tag
        if tagsToCheck.contains(note) {
            return (note, "")
        }

        // Case 3: Note doesn't match known tags or contain separator
        return (customTag, note)
    }

    // Method to handle saving - Uses global constants
    private func saveChanges() {
        let tagPart: String?
        if editedTag == noTag || editedTag == customTag {
            tagPart = nil
        } else {
            tagPart = editedTag
        }

        let notePart = editedNote.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalNote: String?

        if let tag = tagPart, !notePart.isEmpty {
            finalNote = "\(tag) - \(notePart)"
        } else if let tag = tagPart {
            finalNote = tag
        } else if !notePart.isEmpty {
            finalNote = notePart
        } else {
            finalNote = nil
        }

        dataManager.updateGeoPoint(pointId: point.id, newType: editedType, newNote: finalNote)
        dismiss()
    }
}

// Extracted Read-ONLY Details Section (Now internal access)
struct PointDetailsReadOnlySection: View {
    let point: GeoPoint
    let measurementSystem: String

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Details")
                .font(.headline)
                .foregroundColor(Color("TextColor")) // Theme text
                .padding(.bottom, 4)

             HStack {
                Text("Coordinates:")
                    .fontWeight(.medium)
                    .foregroundColor(Color("TextColor")) // Theme text
                Spacer()
                VStack(alignment: .trailing) {
                    Text("Lat: \(point.latitude, specifier: "%.6f")")
                        .foregroundColor(Color("TextLightColor")) // Theme light text
                    Text("Lon: \(point.longitude, specifier: "%.6f")")
                        .foregroundColor(Color("TextLightColor")) // Theme light text
                }
                .font(.caption)
            }

            Divider().overlay(Color("BorderColor")) // Theme divider
                .padding(.vertical, 2)

            if let altitude = point.altitude {
                HStack {
                    Text("Elevation:")
                        .fontWeight(.medium)
                        .foregroundColor(Color("TextColor")) // Theme text
                    Spacer()
                    Text(formatElevation(altitude))
                        .foregroundColor(Color("TextLightColor")) // Theme light text
                }
                Divider().overlay(Color("BorderColor")) // Theme divider
                    .padding(.vertical, 2)
            }

            if let heading = point.heading {
                HStack {
                    Text("Heading:")
                        .fontWeight(.medium)
                        .foregroundColor(Color("TextColor")) // Theme text
                    Spacer()
                    Text("\(Int(heading))°")
                        .foregroundColor(Color("TextLightColor")) // Theme light text
                }
                Divider().overlay(Color("BorderColor")) // Theme divider
                    .padding(.vertical, 2)
            }

            HStack {
                Text("Time:")
                    .fontWeight(.medium)
                    .foregroundColor(Color("TextColor")) // Theme text
                Spacer()
                Text(formatDate(point.timestamp))
                    .foregroundColor(Color("TextLightColor")) // Theme light text
            }
        }
        .padding()
        .background(Color("CardBackgroundColor")) // Use card background
        .cornerRadius(10)
        // Removed shadow
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }

    private func formatElevation(_ elevation: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        if system == .metric {
            return String(format: "%.2f m", elevation)
        } else {
            // Convert meters to feet
            let feet = elevation * 3.28084
            return String(format: "%.2f ft", feet)
        }
    }
}

#Preview {
    // Need a sample GeoPoint for the preview
    let sampleCoord = CLLocationCoordinate2D(latitude: 51.5, longitude: -0.1)
    let samplePoint = GeoPoint(
        coordinate: sampleCoord,
        elevation: 10.0, // This should be 'altitude', but the initializer uses 'elevation' parameter name
        timestamp: Date(),
        type: .marked,
        note: "Sample Note",
        heading: 90.0
    )

    return PointDetailsEditView(point: samplePoint)
        .environmentObject(DataManager()) // Needs DataManager for save button
}
