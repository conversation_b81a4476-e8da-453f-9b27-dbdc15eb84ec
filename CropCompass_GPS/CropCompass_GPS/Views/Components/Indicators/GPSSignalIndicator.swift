import SwiftUI

/// A view component that displays the current GPS signal strength with an icon and optional label
struct GPSSignalIndicator: View {
    @EnvironmentObject var locationManager: LocationManager
    var showLabel: Bool = true
    
    var body: some View {
        HStack {
            Image(systemName: locationManager.signalStrength.icon)
                .foregroundColor(locationManager.signalStrength.color)
                .imageScale(.large)
            
            if showLabel {
                Text(verboseSignalStrength)
                    .foregroundColor(locationManager.signalStrength.color)
                    // Attempt to add identifier here, but might be unstable if text changes often
                    // .accessibilityIdentifier("label.gps.status") 
            }
        }
        // Consider applying identifier to the container if text content is unreliable for testing
        .accessibilityElement(children: .combine) // Combine children for accessibility
        .accessibilityLabel("GPS Status: \(verboseSignalStrength)") // Provide a good overall label
        .accessibilityIdentifier("indicator.gps.status") // Identifier for the whole indicator
    }
    
    private var verboseSignalStrength: String {
        switch locationManager.signalStrength {
        case .none:
            return "No Signal"
        case .poor:
            return "Poor Signal"
        case .moderate:
            return "Moderate Signal"
        case .good:
            return "Good Signal"
        case .excellent:
            return "Excellent Signal"
        }
    }
}

#Preview {
    GPSSignalIndicator()
        .environmentObject(LocationManager(dataManager: DataManager()))
}
