//
//  RecordingView.swift
//  CropCompass_GPS
//
//  Created by <PERSON><PERSON> on 2025-04-05. // Update date if needed
//

import SwiftUI
import CoreLocation
import UserNotifications

// Main recording view
struct RecordingView: View {
    @EnvironmentObject var locationManager: LocationManager
    @EnvironmentObject var dataManager: DataManager
    @AppStorage("measurementSystem") private var measurementSystem: String = MeasurementSystem.metric.rawValue
    @State private var showingMarkSpotSheet = false
    @State private var showingFieldManagement = false
    @State private var showingAddFieldSheet = false
    @State private var newFieldName: String = "" // For AddFieldView sheet
    @State private var showingStartConfirmation = false // State for confirmation alert
    @State private var showingMarkConfirmation = false // State for Mark Spot confirmation

    var body: some View {
        // Apply the accessibility identifier to the NavigationView
        NavigationView {
            VStack(spacing: 15) {
                // Compass Section
                CompassView()
                    .padding(.top, 15)
                    .padding(.bottom, 5)

                // Field Selector with label outside
                FieldSelectorView(showLabel: true, labelText: "Selected Field")
                    //.padding(.horizontal)

                // Location Display Section
                locationSection
                    .padding(.top, -5)

                // Recording Status Section
                recordingStatusSection
                    .padding(.top, -5)

                // Action Buttons Section
                actionButtonsSection

                Spacer() // Push content to the top
            }
            .padding()
            .navigationTitle("Record GPS Points") // Updated Navigation Title
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingMarkSpotSheet) {
                // Restore original logic for presenting the Mark Spot sheet
                if let currentLocation = locationManager.location,
                   let currentFieldID = dataManager.selectedFieldID {
                    MarkSpotNoteView( // Use the actual view for marking spots
                        location: currentLocation,
                        fieldID: currentFieldID
                    ) { note, pointType in // Completion handler from MarkSpotNoteView
                        _ = dataManager.addGeoPoint(
                            coordinate: currentLocation.coordinate,
                            elevation: currentLocation.altitude,
                            timestamp: currentLocation.timestamp,
                            type: pointType, // Use the selected point type
                            note: note,
                            to: currentFieldID
                        )
                        showingMarkSpotSheet = false // Dismiss sheet on completion
                    }
                    .environmentObject(dataManager) // Pass DataManager if needed by MarkSpotNoteView
                    // Pass locationManager too if needed
                    // .environmentObject(locationManager)
                } else {
                    // Handle the case where data is missing when trying to mark spot
                    VStack {
                        Text("Error")
                            .font(.title)
                        Text("Cannot mark spot: Missing location or field selection.")
                            .padding()
                        Button("OK") { showingMarkSpotSheet = false }
                            .buttonStyle(.borderedProminent)
                    }
                    .padding()
                }
            }

             // Sheet for Adding a new Field
             .sheet(isPresented: $showingAddFieldSheet) {
                AddFieldView(fieldName: $newFieldName) { // Closure takes no arguments
                    // Get name from state variable
                    let nameToSave = newFieldName.trimmingCharacters(in: .whitespaces)
                    guard !nameToSave.isEmpty else { return } // Prevent saving empty names
                    _ = dataManager.createField(name: nameToSave)
                    newFieldName = "" // Clear after save
                    showingAddFieldSheet = false // Dismiss sheet
                }
                .environmentObject(dataManager)
             }
            // Field management has been moved to the HomeView
        }
        .navigationViewStyle(.stack)
        .background(Color("BackgroundColor").ignoresSafeArea())
        .accessibilityIdentifier("view.record") // Added identifier HERE
        .alert("Field Has Data", isPresented: $showingStartConfirmation) {
            Button("Continue Recording") {
                startRecordingFlow() // Call the helper to start
            }
            Button("Create New Field") {
                showingAddFieldSheet = true // Show the add field sheet
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This field already has recorded points. Do you want to continue adding points to this field or create a new one?")
        }
        // ADD the SECOND alert modifier here for Mark Spot
        .alert("Field Has Continuous Points", isPresented: $showingMarkConfirmation) {
            Button("Mark Spot Here") {
                showingMarkSpotSheet = true // Show the mark spot sheet
            }
            Button("Create New Field") {
                showingAddFieldSheet = true // Show the add field sheet
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("This field already has continuous tracking points. Do you want to mark a new spot in this field or create a new field first?")
        }
    }

    // Location Display Section
    private var locationSection: some View {
        VStack(spacing: 8) {
            // GPS Signal Strength Indicator
            GPSSignalIndicator()
                .padding(.bottom, 3)

            if let location = locationManager.location {
                HStack {
                    LabelledLocationValueView(
                        label: "Latitude",
                        value: String(format: "%.6f°", location.coordinate.latitude),
                        alignment: .leading,
                        valueFont: .title3,
                        valueWeight: .bold
                    )
                    Spacer()
                    LabelledLocationValueView(
                        label: "Longitude",
                        value: String(format: "%.6f°", location.coordinate.longitude),
                        alignment: .trailing,
                        valueFont: .title3,
                        valueWeight: .bold
                    )
                }

                HStack {
                    LabelledLocationValueView(
                        label: "Elevation",
                        value: formatElevation(location.altitude),
                        alignment: .leading
                    )
                    Spacer()
                    LabelledLocationValueView(
                        label: "Accuracy",
                        value: formatDistance(location.horizontalAccuracy),
                        alignment: .trailing,
                        valueColor: Color.accuracyColor(for: location.horizontalAccuracy)
                    )
                }
            } else {
                Text("Waiting for location data...")
                    .foregroundColor(Color("TextLightColor"))
                    .frame(height: 80)
            }
        }
        .padding(16)
        .background(Color("CardBackgroundColor"))
        .cornerRadius(10)
        //.padding(.horizontal)
    }

    // Recording Status Section
    private var recordingStatusSection: some View {
        VStack(spacing: 10) {
            // Points counter with animation when recording
            HStack {
                Text("Points in field:")
                    .foregroundColor(Color("TextColor"))
                Spacer()
                Text("\(dataManager.selectedField?.geoPoints.count ?? 0)")
                    .bold()
                    .foregroundColor(dataManager.recordingState == .notRecording ? Color("TextColor") : Color("ThemePrimaryColor"))
                    .accessibilityIdentifier("label.points.count")
            }
        }
        .padding(16)
        .background(Color("CardBackgroundColor"))
        .cornerRadius(10)
        //.padding(.horizontal)
    }

    // Action Buttons Section
    private var actionButtonsSection: some View {
        VStack(spacing: 15) {
            // Group Start/Stop and Close Loop buttons horizontally if possible when Close is available
            HStack {
                // Start/Stop Recording Button
                Button {
                    if dataManager.recordingState == .notRecording {
                        // Check if the selected field contains continuous points
                        if let fieldID = dataManager.selectedFieldID, dataManager.fieldContainsContinuousPoints(fieldId: fieldID) {
                            // Field has continuous points, show confirmation alert
                            showingStartConfirmation = true
                        } else if dataManager.selectedFieldID != nil {
                            // Field is empty or has only marked/boundary points, start directly
                            startRecordingFlow()
                        }
                        // If selectedFieldID is nil, button is disabled anyway
                    } else {
                        // Stop recording
                        dataManager.stopContinuousRecording()
                    }
                } label: {
                    Label(
                        dataManager.recordingState == .notRecording ? "Start Recording" : "Stop Recording",
                        systemImage: dataManager.recordingState == .notRecording ? "play.circle.fill" : "stop.circle.fill"
                    )
                    .font(.title3)
                    .frame(maxWidth: .infinity) // Make buttons expand
                    .padding()
                }
                .buttonStyle(.borderedProminent)
                .tint(dataManager.recordingState == .notRecording ? Color("ThemePrimaryColor") : Color("ErrorColor"))
                .disabled(dataManager.selectedFieldID == nil || locationManager.location == nil)
                .accessibilityIdentifier(dataManager.recordingState == .notRecording ? "button.recording.start" : "button.recording.stop")

                // Conditionally show Close Loop button
                if dataManager.canCloseLoop {
                    Button {
                        dataManager.closeLoop()
                    } label: {
                        Label("Close Loop", systemImage: "arrow.counterclockwise.circle.fill")
                            .font(.title3)
                            .frame(maxWidth: .infinity) // Make buttons expand
                            .padding()
                    }
                    .buttonStyle(.borderedProminent)
                    .tint(.green) // Use a distinct color for Close Loop
                    .accessibilityIdentifier("button.recording.closeloop")
                    .transition(.scale.combined(with: .opacity)) // Add animation
                }
            }

            // Mark Spot Button
            Button {
                guard locationManager.location != nil else {
                    // TODO: Show an alert here if location is nil?
                    print("Cannot mark spot: Location data not available.")
                    return
                }

                // Check conditions to bypass confirmation
                if dataManager.recordingState == .recordingContinuous {
                    // When recording is in progress, bypass confirmation
                    showingMarkSpotSheet = true
                } else if let fieldID = dataManager.selectedFieldID, dataManager.fieldContainsOnlyMarkedSpots(fieldId: fieldID) {
                    // When field only contains marked spots (no continuous points), bypass confirmation
                    showingMarkSpotSheet = true
                } else if let selectedField = dataManager.selectedField, !selectedField.geoPoints.isEmpty {
                    // Field has continuous data and not recording, show confirmation
                    showingMarkConfirmation = true
                } else if dataManager.selectedFieldID != nil {
                    // Field is empty, proceed directly
                    showingMarkSpotSheet = true
                }
                // If selectedFieldID is nil, button is disabled anyway

            } label: {
                HStack {
                    Spacer()
                    Label("Mark Spot", systemImage: "mappin.and.ellipse")
                        .font(.title3)
                        .padding()
                    Spacer()
                }
            }
            // Style consistently
            .buttonStyle(.borderedProminent)
            .tint(Color("ThemeSecondaryColor")) // Use secondary theme color
            .disabled(dataManager.selectedFieldID == nil || locationManager.location == nil)
            .accessibilityIdentifier("button.point.save") // Assuming this button saves the marked spot
        }
    }

    // Helper function to start the actual recording
    private func startRecordingFlow() {
        // Request authorization first to ensure we have the right permissions
        locationManager.requestAuthorisation()

        // Start location updates if not already running
        if !locationManager.isUpdatingLocation {
            locationManager.startUpdatingLocation()
        }

        // Start recording in the data manager
        dataManager.startContinuousRecording()

        // Inform the user that recording will continue in the background
        if locationManager.authorisationStatus == .authorizedAlways {
            // Only show this message if we have "Always" authorization
            let content = UNMutableNotificationContent()
            content.title = "Recording in Progress"
            content.body = "GPS recording will continue in the background"
            content.sound = .default

            // Create a notification request
            let request = UNNotificationRequest(identifier: "backgroundRecording", content: content, trigger: nil)

            // Add the request to the notification center
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("Error showing notification: \(error.localizedDescription)")
                }
            }
        }
    }

    // Helper functions for unit conversion
    private func formatElevation(_ elevation: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        if system == .metric {
            return String(format: "%.2f m", elevation)
        } else {
            // Convert meters to feet
            let feet = elevation * 3.28084
            return String(format: "%.2f ft", feet)
        }
    }

    private func formatDistance(_ distance: Double) -> String {
        let system = MeasurementSystem(rawValue: measurementSystem) ?? .metric
        if system == .metric {
            return String(format: "%.1f m", distance)
        } else {
            // Convert meters to feet
            let feet = distance * 3.28084
            return String(format: "%.1f ft", feet)
        }
    }
}

// Placeholder for GPSSignalIndicator if not defined elsewhere
/* // DELETE START
// If GPSSignalIndicator is in another file, ensure it's imported or remove this.
struct GPSSignalIndicator: View {
     @EnvironmentObject var locationManager: LocationManager
     var body: some View {
         // Example implementation - replace with your actual indicator
         HStack {
             Text("GPS:")
             Text(locationManager.gpsStatusDescription)
                 .foregroundColor(locationManager.gpsStatusColor)
                 // IMPORTANT: Apply the identifier HERE if possible
                 // .accessibilityIdentifier("label.gps.status")
         }
         // Or apply identifier to the HStack if text changes too much
         .accessibilityElement(children: .combine) // Combine children for accessibility
         .accessibilityLabel("GPS Status: \(locationManager.gpsStatusDescription)") // Provide a good label
         // .accessibilityIdentifier("indicator.gps.status") // Consider identifying the container
     }
}
*/ // DELETE END

// Add Previews if desired
struct RecordingView_Previews: PreviewProvider {
    static var previews: some View {
        let dataManager = DataManager()
        let locationManager = LocationManager(dataManager: dataManager)

        // Create a sample field
        let field = dataManager.createField(name: "Test Field")
        dataManager.selectedFieldID = field.id

        return RecordingView()
            .environmentObject(locationManager)
            .environmentObject(dataManager)
    }
}