import Foundation

// MARK: - Shared Tag Constants

// Special tag strings
internal let noTag = "Select Tag"
internal let customTag = "Custom Note..."

// Define tag options based on point type
internal let regularTags: [String] = [
    noTag,
    "Building / Shed",
    "Ditch",
    "Obstruction",
    "Pond / Wet Area",
    "Road",
    "Rock Formation",
    "Sample Point",
    "Stream / River",
    "Tree",
    "Weed Patch",
    customTag
]

internal let boundaryTags: [String] = [
    noTag,
    "Boundary Point",
    "Corner Point",
    "Gate",
    customTag
] 