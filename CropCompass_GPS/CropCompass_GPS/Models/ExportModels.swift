import Foundation
import UniformTypeIdentifiers // Needed for UTType

// Enum defining the available export formats
enum ExportFormat: String, CaseIterable, Identifiable {
    case gpx = "GPX"
    case kml = "KML"
    case csv = "CSV"
    
    var id: String { self.rawValue }
    
    // Provides the corresponding Uniform Type Identifier
    var utType: UTType {
        switch self {
        case .gpx: return .gpx // Use the custom extension defined elsewhere
        case .kml: return .kml
        case .csv: return .commaSeparatedText
        }
    }
    
    // Provides the appropriate file extension
    var fileExtension: String {
        switch self {
        case .gpx: return "gpx"
        case .kml: return "kml"
        case .csv: return "csv"
        }
    }
}

// Struct to make exported files easily shareable via ShareSheet
struct ShareableDocument: Identifiable {
    let id = UUID()
    let url: URL // URL of the temporary file to share
}

// Custom UTType definition for GPX if not already defined globally
// Ensure this extension is available where UTType.gpx is used.
// If you already have this UTType extension elsewhere (e.g., in ExportView.swift),
// you might not need to repeat it here.
extension UTType {
    static var gpx: UTType {
        // Match the identifier used in Info.plist for Exported Type Identifiers
        UTType(exportedAs: "com.topografix.gpx") 
    }
    static var kml: UTType {
        // Standard KML type
        UTType(filenameExtension: "kml", conformingTo: .xml)! 
    }
} 