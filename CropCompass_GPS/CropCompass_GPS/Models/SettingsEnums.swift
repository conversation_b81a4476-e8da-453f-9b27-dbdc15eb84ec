import SwiftUI
import MapKit

// Enum for map style choices in settings
// Explicitly conform to Hashable
enum MapStyleChoice: String, CaseIterable, Identifiable, Hashable {
    case standard = "Standard"
    case hybrid = "Hybrid"
    case imagery = "Satellite" // Renamed from imagery for clarity

    var id: String { self.rawValue }

    var displayName: String {
        self.rawValue // Use the raw value directly
    }

    // Convert choice to MapKit's MapStyle
    var mapKitStyle: MapStyle {
        switch self {
        case .standard:
            return .standard(elevation: .realistic)
        case .hybrid:
            return .hybrid(elevation: .realistic)
        case .imagery:
            return .imagery(elevation: .realistic)
        }
    }

    // Add the missing iconName property
    var iconName: String {
        switch self {
        case .standard: return "map"
        case .hybrid: return "map.fill"
        case .imagery: return "photo.stack"
        }
    }
}

// Enum for measurement system choices
enum MeasurementSystem: String, CaseIterable, Identifiable {
    case metric = "Metric"
    case imperial = "Imperial"

    var id: String { self.rawValue }

    var displayName: String {
        switch self {
        case .metric:
            return "Metric (m, km, ha)"
        case .imperial:
            return "Imperial (ft, mi, ac)"
        }
    }

    // Default distance unit for display
    var distanceUnit: String {
        switch self {
        case .metric: return "m"
        case .imperial: return "ft"
        }
    }

    // Default large distance unit for display
    var largeDistanceUnit: String {
        switch self {
        case .metric: return "km"
        case .imperial: return "mi"
        }
    }

    // Default area unit for display
    var areaUnit: String {
        switch self {
        case .metric: return "ha"
        case .imperial: return "ac"
        }
    }

    // Default small area unit for display
    var smallAreaUnit: String {
        switch self {
        case .metric: return "m²"
        case .imperial: return "ft²"
        }
    }
}