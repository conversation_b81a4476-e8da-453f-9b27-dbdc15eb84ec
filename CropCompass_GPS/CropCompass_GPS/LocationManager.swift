import Foundation
import CoreLocation
import <PERSON><PERSON>

// Helper for detecting SwiftUI previews
struct PreviewEnvironmentKey {
    // Multiple checks to detect preview environment
    static var inPreview: Bool {
        // Check 1: Process name contains "previews"
        let processName = ProcessInfo.processInfo.processName.lowercased()
        if processName.contains("preview") {
            return true
        }

        // Check 2: Environment variable check
        if ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1" {
            return true
        }

        // Check 3: Check for PreviewsInjection framework in loaded bundles
        for bundle in Bundle.allBundles {
            let bundlePath = bundle.bundlePath.lowercased()
            if bundlePath.contains("previewsinjection") {
                return true
            }
        }

        return false
    }
}

// Use global NSLog function
fileprivate let NSLog = Foundation.NSLog

// Signal strength enum
enum GPSSignalStrength {
    case none, poor, moderate, good, excellent

    // Use theme colors
    var color: Color {
        switch self {
        case .none: return Color("ErrorColor")
        case .poor: return Color("WarningColor")
        case .moderate: return Color("WarningColor").opacity(0.8) // Slightly lighter warning color
        case .good: return Color("SuccessColor")
        case .excellent: return Color("ThemePrimaryColor")
        }
    }

    var icon: String {
        switch self {
        case .none: return "location.slash"
        case .poor: return "location"
        case .moderate, .good, .excellent: return "location.fill"
        }
    }
}

// Manages location services for the app.
class LocationManager: NSObject, ObservableObject, CLLocationManagerDelegate {
    // The core object for managing location services.
    private let manager = CLLocationManager()
    // Hold a reference to the DataManager
    private weak var dataManager: DataManager?

    // Published properties will automatically update SwiftUI views when they change.
    @Published var location: CLLocation? // The latest received location.
    @Published var authorisationStatus: CLAuthorizationStatus = .notDetermined
    @Published var accuracyAuthorisation: CLAccuracyAuthorization = .reducedAccuracy
    @Published var isUpdatingLocation: Bool = false

    // Heading properties
    @Published var heading: CLHeading?
    @Published var headingAccuracy: Double = -1
    @Published var isUpdatingHeading: Bool = false

    // Properties accessible for settings
    @Published var distanceFilter: Double = 5.0
    @Published var activityType: CLActivityType = .fitness // Default to walking

    // GPS Signal Strength properties
    @Published var signalStrength: GPSSignalStrength = .none

    // Modified initializer to accept DataManager
    init(dataManager: DataManager) {
        Foundation.NSLog("LM_DEBUG: Initializing LocationManager with DataManager...") // Added log
        self.dataManager = dataManager // Store the reference
        super.init()
        manager.delegate = self // Set this class to receive location updates.
        // Configure desired accuracy and activity type (can be adjusted later)
        manager.desiredAccuracy = kCLLocationAccuracyBestForNavigation
        manager.activityType = activityType
        manager.distanceFilter = distanceFilter
        NSLog("LM_DEBUG: Initial activity type set to \(activityType)")

        // Check if we're in preview mode
        let isInPreviewMode = PreviewEnvironmentKey.inPreview
        NSLog("LM_DEBUG: Preview mode detection: \(isInPreviewMode)")

        // Do NOT configure background updates here; wait for authorisation.
        // if !isInPreviewMode {
        //     manager.allowsBackgroundLocationUpdates = true
        //     manager.pausesLocationUpdatesAutomatically = false
        //     NSLog("LM_DEBUG: Background location updates enabled")
        // } else {
        //     manager.allowsBackgroundLocationUpdates = false
        //     manager.pausesLocationUpdatesAutomatically = true
        //     NSLog("LM_DEBUG: Running in preview mode, background location updates explicitly disabled")
        // }

        // Only show indicator if not in preview
        manager.showsBackgroundLocationIndicator = !isInPreviewMode

        NSLog("LM_DEBUG: Requesting authorisation from init...")
        requestAuthorisation()
        // Initial update based on current status (in case already authorised)
        updateBackgroundCapabilities(for: manager.authorizationStatus)
        NSLog("LM_DEBUG: LocationManager init finished.")
    }

    // Convenience init for previews or cases where DataManager isn't needed immediately
    override convenience init() {
        NSLog("LM_DEBUG: Initializing LocationManager with CONVENIENCE init...") // Added log
        // This will crash if DataManager methods are called without setting it later.
        // Use cautiously, primarily for #Preview.
        // A better approach for previews might involve a mock DataManager.
        self.init(dataManager: DataManager()) // Provide a dummy DataManager

        // Log preview detection status
        let isInPreviewMode = PreviewEnvironmentKey.inPreview
        NSLog("LM_DEBUG: Convenience init - Preview mode detection: \(isInPreviewMode)")

        print("Warning: LocationManager initialized without a specific DataManager. Ensure it's set if needed.")

        // In preview mode, we don't need to request authorization
        if !isInPreviewMode {
            NSLog("LM_DEBUG: Requesting authorisation from convenience init...")
            requestAuthorisation()
            // Initial update based on current status
            updateBackgroundCapabilities(for: manager.authorizationStatus)
        } else {
            NSLog("LM_DEBUG: Skipping authorization request in preview mode")
            // Ensure background updates are off for previews
            updateBackgroundCapabilities(for: .notDetermined) // Pass a status that disables background
        }

        // Do NOT start updates here; wait for authorisation confirmation
        // startUpdatingLocation()
        NSLog("LM_DEBUG: LocationManager convenience init finished.")
    }

    // Requests user permission for location access.
    func requestAuthorisation() {
        NSLog("LM_DEBUG: Entering requestAuthorisation()") // Kept log

        // Check the current status before requesting
        let currentStatus = manager.authorizationStatus
        NSLog("LM_DEBUG: Current status before request: \(currentStatus.description)") // Use NSLog

        if currentStatus == .notDetermined {
            // Request "When In Use" authorisation first.
            // The plist must contain NSLocationWhenInUseUsageDescription.
            NSLog("LM_DEBUG: Requesting When In Use authorization...") // Use NSLog
            manager.requestWhenInUseAuthorization()
        } else if currentStatus == .authorizedWhenInUse {
             // Request Always authorization for background updates
             NSLog("LM_DEBUG: Already authorized WhenInUse. Requesting Always authorization for background updates.") // Use NSLog
             manager.requestAlwaysAuthorization()
        } else {
             NSLog("LM_DEBUG: Authorization status is already determined ('\(currentStatus.description)') and not 'notDetermined' or 'authorizedWhenInUse'. No new request sent.") // Added else log
        }
         NSLog("LM_DEBUG: Exiting requestAuthorisation()") // Added log
    }

    // Starts receiving location updates.
    func startUpdatingLocation() {
        NSLog("LM_DEBUG: Entering startUpdatingLocation()") // Kept log

        // Check if we're in preview mode - don't start updates in preview
        if PreviewEnvironmentKey.inPreview {
            NSLog("LM_DEBUG: In preview mode - not starting real location updates")
            // Just update the state variable for UI consistency
            isUpdatingLocation = true
            return
        }

        // *** Check the manager's status directly to avoid race conditions with the published property ***
        let currentManagerStatus = manager.authorizationStatus
        if currentManagerStatus == .authorizedWhenInUse || currentManagerStatus == .authorizedAlways {
            NSLog("LM_DEBUG: Authorised (checked manager status: \(currentManagerStatus.description)). Calling manager.startUpdatingLocation(). Current isUpdatingLocation: \(isUpdatingLocation)")
            if !isUpdatingLocation { // Avoid redundant calls if already updating
                 manager.startUpdatingLocation()
                 isUpdatingLocation = true
            } else {
                 NSLog("LM_DEBUG: Already updating, startUpdatingLocation() call skipped.")
            }
        } else {
            NSLog("LM_DEBUG: Cannot start location updates: Not authorised (checked manager status: \(currentManagerStatus.description)).")
            // *** Do NOT request authorisation here - let the delegate handle the flow ***
            // if authorisationStatus == .notDetermined {
            //     requestAuthorisation()
            // }
        }
        NSLog("LM_DEBUG: Exiting startUpdatingLocation()") // Added log
    }

    // Stops receiving location updates.
    func stopUpdatingLocation() {
        NSLog("LM_DEBUG: Entering stopUpdatingLocation(). Current isUpdatingLocation: \(isUpdatingLocation)") // Added log
        if isUpdatingLocation {
            manager.stopUpdatingLocation()
            isUpdatingLocation = false
            NSLog("LM_DEBUG: Called manager.stopUpdatingLocation().")
        } else {
            NSLog("LM_DEBUG: Already stopped. No action taken.")
        }
        NSLog("LM_DEBUG: Exiting stopUpdatingLocation()") // Added log
    }

    // Heading methods
    func startUpdatingHeading() {
        NSLog("LM_DEBUG: Entering startUpdatingHeading()")
        if CLLocationManager.headingAvailable() {
            NSLog("LM_DEBUG: Heading is available. Starting heading updates.")
            manager.startUpdatingHeading()
            isUpdatingHeading = true
        } else {
            NSLog("LM_DEBUG: Heading is not available on this device.")
        }
        NSLog("LM_DEBUG: Exiting startUpdatingHeading()")
    }

    func stopUpdatingHeading() {
        NSLog("LM_DEBUG: Entering stopUpdatingHeading(). Current isUpdatingHeading: \(isUpdatingHeading)")
        if isUpdatingHeading {
            manager.stopUpdatingHeading()
            isUpdatingHeading = false
            NSLog("LM_DEBUG: Called manager.stopUpdatingHeading().")
        } else {
            NSLog("LM_DEBUG: Heading updates already stopped. No action taken.")
        }
        NSLog("LM_DEBUG: Exiting stopUpdatingHeading()")
    }

    // GPS Signal Strength methods
    func updateSignalStrength() {
        if let location = location {
            // Determine signal based on horizontal accuracy
            switch location.horizontalAccuracy {
            case ..<0:
                signalStrength = .none // Accuracy shouldn't be negative
            case 0..<10:
                 signalStrength = .excellent
            case 10..<20:
                 signalStrength = .good
            case 20..<50:
                 signalStrength = .moderate
            default: // Accuracy >= 50 or negative (error case)
                 signalStrength = .poor
            }
        } else {
            // Explicitly set to none if no location data is available
            signalStrength = .none
        }
    }

    // MARK: - CLLocationManagerDelegate Methods

    // Called when the authorisation status changes.
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        let status = manager.authorizationStatus
        let accuracy = manager.accuracyAuthorization
        NSLog("LM_DEBUG: locationManagerDidChangeAuthorization called. New status: \(status.description), Accuracy: \(accuracy == .fullAccuracy ? "Full" : "Reduced")") // Use NSLog

        // Update published properties on the main thread
        DispatchQueue.main.async {
            self.authorisationStatus = status
            self.accuracyAuthorisation = accuracy
            NSLog("LM_DEBUG: Updated published authorisationStatus and accuracyAuthorisation.")

            // Configure background capabilities based on the new status
            self.updateBackgroundCapabilities(for: status)

            // Decide further actions based on the new status
            switch status {
            case .authorizedWhenInUse:
                // User granted When In Use. If background is needed, request Always.
                NSLog("LM_DEBUG: Authorised WhenInUse. Requesting Always for background...")
                self.requestAuthorisation() // Will now request Always
                // Start updates if not already started (for When In Use scenarios)
                if !self.isUpdatingLocation {
                     NSLog("LM_DEBUG: Starting location updates (WhenInUse granted).")
                     self.startUpdatingLocation()
                }
                // If precise location isn't granted, request it
                if accuracy == .reducedAccuracy {
                    NSLog("LM_DEBUG: Requesting temporary full accuracy...")
                    // Consider the purpose key carefully based on your features
                    manager.requestTemporaryFullAccuracyAuthorization(withPurposeKey: "FieldMappingAccuracy")
                }

            case .authorizedAlways:
                // User granted Always. Start updates if not already started.
                NSLog("LM_DEBUG: Authorised Always.")
                if !self.isUpdatingLocation {
                    NSLog("LM_DEBUG: Starting location updates (Always granted).")
                    self.startUpdatingLocation()
                }
                // If precise location isn't granted, request it (might be needed if initially reduced)
                 if accuracy == .reducedAccuracy {
                     NSLog("LM_DEBUG: Requesting temporary full accuracy (already Always auth)...")
                     manager.requestTemporaryFullAccuracyAuthorization(withPurposeKey: "FieldMappingAccuracy")
                 }

            case .denied, .restricted:
                // User denied or restricted location access. Stop updates and handle appropriately.
                NSLog("LM_DEBUG: Location access denied or restricted. Stopping updates.")
                self.stopUpdatingLocation()
                // Maybe show an alert or guide the user to settings.
                // You might want to add UI feedback here.

            case .notDetermined:
                // Should not typically happen here after initial request, but handle defensively.
                 NSLog("LM_DEBUG: Authorisation status became NotDetermined. Requesting again.")
                 self.requestAuthorisation()

            @unknown default:
                NSLog("LM_DEBUG: Unknown authorisation status: \(status). Stopping updates.")
                self.stopUpdatingLocation()
            }
        }
    }

    // Called when location updates are received.
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        // Process the latest location
        guard let newLocation = locations.last else {
             NSLog("LM_DEBUG: didUpdateLocations called but received empty or nil location array.")
             return
        }
         NSLog("LM_DEBUG: Received location update: Lat \(newLocation.coordinate.latitude), Lon \(newLocation.coordinate.longitude), Acc \(newLocation.horizontalAccuracy)m")

        // Check if the location meets accuracy requirements before processing
        guard newLocation.horizontalAccuracy >= 0 else {
             NSLog("LM_DEBUG: Ignoring location update: Invalid accuracy (negative).")
             return
        }

        // Filter out old or inaccurate points if needed (can be stricter)
        let age = -newLocation.timestamp.timeIntervalSinceNow
        guard age < 10.0 else { // Don't process points older than 10 seconds
             NSLog("LM_DEBUG: Ignoring location update: Too old (\(age)s).")
             return
        }

        // Update the published location on the main thread for UI updates
        DispatchQueue.main.async {
            self.location = newLocation
            self.updateSignalStrength() // Update signal strength based on the new location
            NSLog("LM_DEBUG: Published new location and updated signal strength.")
        }

        // Pass the valid location to DataManager for saving if recording continuously
        // Ensure dataManager is not nil before calling its methods
        if let dm = dataManager, dm.recordingState == .recordingContinuous, let fieldID = dm.selectedFieldID {
             NSLog("LM_DEBUG: Passing continuous location to DataManager for field ID \(fieldID)...")
             // Call the addGeoPoint method, explicitly discard the result
             _ = dm.addGeoPoint(
                coordinate: newLocation.coordinate,
                elevation: newLocation.altitude,
                timestamp: newLocation.timestamp,
                type: .continuous, // Mark this point as continuous
                note: nil,         // No note for continuous points
                to: fieldID
             )
             // After successfully adding a point, check proximity to start
             NSLog("LM_DEBUG: Checking proximity status...")
             dm.updateProximityStatus(currentLocation: newLocation)
        } else {
             // Log why point wasn't added (optional, can be noisy)
             // if dataManager == nil { NSLog("LM_DEBUG: DataManager is nil, cannot save point.") }
             // else if dataManager?.recordingState != .recordingContinuous { NSLog("LM_DEBUG: Not in continuous recording state.") }
             // else if dataManager?.selectedFieldID == nil { NSLog("LM_DEBUG: No field selected in DataManager.") }
        }
    }

    // Called when an error occurs in location manager.
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        // Handle location errors
        NSLog("LM_DEBUG: Location manager failed with error: \(error.localizedDescription)") // Use NSLog

        // Check for specific errors if needed
        if let clErr = error as? CLError {
            switch clErr.code {
            case .denied:
                NSLog("LM_DEBUG: CLError: Location access denied by user.")
                // Ensure updates are stopped and status reflects denial
                DispatchQueue.main.async {
                    self.authorisationStatus = .denied // Update status if needed
                    self.stopUpdatingLocation()
                }
            case .locationUnknown:
                NSLog("LM_DEBUG: CLError: Location could not be determined right now.")
                // This is often temporary, maybe update UI to show searching/weak signal
                 DispatchQueue.main.async {
                     self.signalStrength = .none // Reflect inability to get location
                 }
            // Handle other specific CLError codes as needed
            default:
                NSLog("LM_DEBUG: Unhandled CLError code: \(clErr.code)")
            }
        } else {
            // Handle non-CLError errors if necessary
             NSLog("LM_DEBUG: Received a non-CLError: \(error)")
        }
    }

    // Called when heading updates are received.
    func locationManager(_ manager: CLLocationManager, didUpdateHeading newHeading: CLHeading) {
        // Process the latest heading
         //NSLog("LM_DEBUG: Received heading update: \(newHeading.trueHeading) degrees")

        DispatchQueue.main.async {
            self.heading = newHeading
            self.headingAccuracy = newHeading.headingAccuracy
            //NSLog("LM_DEBUG: Published new heading.")
        }
    }

    // MARK: - Helper Methods

    // Helper to configure background capabilities based on authorisation status
    private func updateBackgroundCapabilities(for status: CLAuthorizationStatus) {
        let isInPreviewMode = PreviewEnvironmentKey.inPreview
        NSLog("LM_DEBUG: Updating background capabilities for status \(status.description). Preview mode: \(isInPreviewMode)")

        if !isInPreviewMode {
            if status == .authorizedAlways {
                manager.allowsBackgroundLocationUpdates = true
                manager.pausesLocationUpdatesAutomatically = false
                manager.showsBackgroundLocationIndicator = true
                 NSLog("LM_DEBUG: Status is authorizedAlways. Enabled background updates.")
            } else {
                // For any other status (including .authorizedWhenInUse), disable background updates.
                manager.allowsBackgroundLocationUpdates = false
                manager.pausesLocationUpdatesAutomatically = true // Let system manage pausing
                manager.showsBackgroundLocationIndicator = false // Don't show indicator if not truly background capable
                 NSLog("LM_DEBUG: Status is NOT authorizedAlways (\'\(status.description)\'). Disabled background updates.")
            }
        } else {
             // Ensure background updates remain off in preview mode regardless of status
             manager.allowsBackgroundLocationUpdates = false
             manager.pausesLocationUpdatesAutomatically = true
             manager.showsBackgroundLocationIndicator = false
             NSLog("LM_DEBUG: In preview mode. Ensured background updates are disabled.")
        }
    }

    // Public methods to update settings
    func updateDistanceFilter(_ newValue: CLLocationDistance) {
        NSLog("LM_DEBUG: Updating distance filter to \(newValue) meters.")
        distanceFilter = newValue
        manager.distanceFilter = newValue
    }

    func updateActivityType(_ newType: CLActivityType) {
        NSLog("LM_DEBUG: Updating activity type to \(newType.description)")
        activityType = newType
        manager.activityType = newType
    }
}

// Extension for CLAuthorizationStatus description
extension CLAuthorizationStatus {
    var description: String {
        switch self {
        case .notDetermined: return "Not Determined"
        case .restricted: return "Restricted"
        case .denied: return "Denied"
        case .authorizedAlways: return "Authorized Always"
        case .authorizedWhenInUse: return "Authorized When In Use"
        @unknown default: return "Unknown"
        }
    }
}

// Extension for CLActivityType description (Optional, for logging)
extension CLActivityType {
     var description: String {
         switch self {
         case .automotiveNavigation: return "Automotive Navigation"
         case .fitness: return "Fitness"
         case .otherNavigation: return "Other Navigation"
         case .other: return "Other"
         case .airborne: return "Airborne" // Added for completeness
         @unknown default: return "Unknown Activity Type"
         }
     }
}