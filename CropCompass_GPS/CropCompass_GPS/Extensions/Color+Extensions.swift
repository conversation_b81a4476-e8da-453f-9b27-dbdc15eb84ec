import SwiftUI
import CoreLocation // Needed for Double type, although not strictly required just for accuracy

extension Color {

    /// Determines the appropriate color based on GPS horizontal accuracy.
    /// - Parameter accuracy: The horizontal accuracy in meters (negative if invalid).
    /// - Returns: A Color (.gray, .green, .orange, or .red).
    static func accuracyColor(for accuracy: Double) -> Color {
        if accuracy < 0 { // CLLocationAccuracy is -1 if invalid
            return Color("TextLightColor")
        } else if accuracy <= 5.0 {
            return Color("SuccessColor")
        } else if accuracy <= 10.0 {
            return Color("WarningColor")
        } else {
            return Color("ErrorColor")
        }
    }

    // Add other shared Color helpers here in the future if needed.
}