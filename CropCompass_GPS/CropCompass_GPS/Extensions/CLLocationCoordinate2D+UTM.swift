import Foundation
import CoreLocation

// Hemisphere enum to replace the one from UTMConversion
enum UTMHemisphere {
    case northern
    case southern
}

// Custom UTMCoordinate struct to replace the one from UTMConversion
struct UTMCoordinate {
    let easting: Double
    let northing: Double
    let zone: UInt
    let hemisphere: UTMHemisphere
}

// Simple struct to hold UTM coordinates for our app
struct LocalUTMCoordinate {
    let easting: Double
    let northing: Double
    let zone: UInt
    let hemisphere: String

    init(from utmCoordinate: UTMCoordinate) {
        self.easting = utmCoordinate.easting
        self.northing = utmCoordinate.northing
        self.zone = utmCoordinate.zone
        self.hemisphere = utmCoordinate.hemisphere == .northern ? "N" : "S"
    }

    init(easting: Double, northing: Double, zone: UInt, hemisphere: String) {
        self.easting = easting
        self.northing = northing
        self.zone = zone
        self.hemisphere = hemisphere
    }
}

extension CLLocationCoordinate2D {
    // Basic implementation of UTM conversion
    // This is a simplified version and may not be as accurate as the UTMConversion library
    // for all edge cases, but should work well for most agricultural field calculations
    func toUTMCoordinate() -> UTMCoordinate {
        // Constants for WGS84 ellipsoid
        let a = 6378137.0         // semi-major axis
        let f = 1.0 / 298.257223563 // flattening
        let e = sqrt(2.0 * f - f * f) // eccentricity

        let latRad = self.latitude * .pi / 180.0
        let lonRad = self.longitude * .pi / 180.0

        // Determine zone
        let zone = UInt(floor((self.longitude + 180.0) / 6.0) + 1)

        // Determine hemisphere
        let hemisphere: UTMHemisphere = self.latitude >= 0 ? .northern : .southern

        // Central meridian of the zone
        let centralMeridian = Double(zone) * 6.0 - 183.0
        let centralMeridianRad = centralMeridian * .pi / 180.0

        // Calculate UTM parameters
        let k0 = 0.9996 // scale factor

        // Compute meridian distance
        let n = f / (2.0 - f)
        let A = a * (1.0 - n + (5.0 * n * n / 4.0) * (1.0 - n) + (81.0 * pow(n, 4.0) / 64.0) * (1.0 - n))
        let alpha = [ 0.0,
                      (3.0 / 2.0) * n - (27.0 / 32.0) * pow(n, 3.0),
                      (21.0 / 16.0) * pow(n, 2.0) - (55.0 / 32.0) * pow(n, 4.0),
                      (151.0 / 96.0) * pow(n, 3.0),
                      (1097.0 / 512.0) * pow(n, 4.0) ]

        var meridianDistance = A * latRad
        for j in 1...4 {
            meridianDistance += alpha[j] * sin(2.0 * Double(j) * latRad)
        }

        // Compute easting and northing
        let nu = a / sqrt(1.0 - e * e * sin(latRad) * sin(latRad))
        let p = lonRad - centralMeridianRad

        let term1 = nu * sin(latRad) * cos(latRad) * pow(p, 2.0) / 2.0
        let term2 = nu * sin(latRad) * pow(cos(latRad), 3.0) * (5.0 - tan(latRad) * tan(latRad) + 9.0 * e * e * pow(cos(latRad), 2.0)) * pow(p, 4.0) / 24.0

        let term3 = nu * cos(latRad) * p
        let term4 = nu * pow(cos(latRad), 3.0) * (1.0 - tan(latRad) * tan(latRad) + e * e * pow(cos(latRad), 2.0)) * pow(p, 3.0) / 6.0

        var northing = k0 * (meridianDistance + term1 + term2)
        var easting = k0 * (term3 + term4)

        // False easting and northing
        easting += 500000.0 // 500km offset for easting

        // In southern hemisphere, add 10,000,000 meter offset to northing
        if hemisphere == .southern {
            northing += 10000000.0
        }

        return UTMCoordinate(easting: easting, northing: northing, zone: zone, hemisphere: hemisphere)
    }
}
