import Foundation
import CoreLocation // Needed for CLLocationCoordinate2D

// Enum to distinguish between different types of recorded points.
enum PointType: String, Codable, CaseIterable {
    case continuous // Point recorded automatically during continuous tracking
    case marked     // Point manually marked by the user (potentially with a note)
    case boundary   // Boundary/corner point of a field (typically with a note indicating which corner)
}

// Represents a single recorded geographical point.
struct GeoPoint: Identifiable, Codable, Hashable {
    let id: UUID
    let latitude: Double
    let longitude: Double
    let altitude: Double?
    let timestamp: Date
    var pointType: PointType // Changed from let to var
    var note: String?       // Changed from let to var
    let heading: Double?    // Keep as let if not editable
    
    // Convenience Initializer using CLLocationCoordinate2D
    init(coordinate: CLLocationCoordinate2D, elevation: Double?, timestamp: Date, type: PointType, note: String? = nil, heading: Double? = nil) {
        self.id = UUID()
        self.latitude = coordinate.latitude
        self.longitude = coordinate.longitude
        self.altitude = elevation
        self.timestamp = timestamp
        self.pointType = type
        self.note = note
        self.heading = heading
    }
    
    // Add the primary initializer if needed for direct lat/lon usage or Codable
    init(id: UUID = UUID(), latitude: Double, longitude: Double, altitude: Double?, timestamp: Date, pointType: PointType, note: String?, heading: Double?) {
        self.id = id
        self.latitude = latitude
        self.longitude = longitude
        self.altitude = altitude
        self.timestamp = timestamp
        self.pointType = pointType
        self.note = note
        self.heading = heading
    }
}

// Represents a field for recording GPS points
struct Field: Identifiable, Codable {
    var id = UUID() // Unique identifier
    var name: String
    var geoPoints: [GeoPoint] = [] // Array to store recorded points for this field
}
