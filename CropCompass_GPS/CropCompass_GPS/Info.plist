<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>location</string>
	</array>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to accurately record GPS points for your fields while you are using the app.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to continuously record GPS points for your fields, even when the app is in the background.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>FieldMappingAccuracy</key>
		<string>Precise location is temporarily needed to ensure accurate placement of recorded points and field boundaries.</string>
	</dict>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>com.topografix.gpx</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>gpx</string>
				</array>
				<key>public.mime-type</key>
				<string>application/gpx+xml</string>
			</dict>
			<key>UTTypeDescription</key>
			<string>GPX File</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.xml</string>
			</array>
		</dict>
	</array>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>GPX File</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.topografix.gpx</string>
			</array>
			<key>LSHandlerRank</key>
			<string>Owner</string>
		</dict>
	</array>
</dict>
</plist>
