import XCTest
import CoreLocation
@testable import CropCompass_GPS // Ensure this matches your module name

final class DataManagerTests: XCTestCase {

    var dataManager: DataManager!
    let testSuiteName = "DataManagerTestsDefaults"

    // MARK: - Setup & Teardown

    override func setUpWithError() throws {
        // Use a specific UserDefaults suite for testing
        guard let testDefaults = UserDefaults(suiteName: testSuiteName) else {
            XCTFail("Failed to create UserDefaults suite for testing")
            return
        }
        testDefaults.removePersistentDomain(forName: testSuiteName) // Clear previous test data
        dataManager = DataManager(userDefaults: testDefaults) // Initialize with test defaults
        XCTAssertTrue(dataManager.fields.isEmpty, "DataManager should start with no fields")
    }

    override func tearDownWithError() throws {
        // Clean up
        dataManager = nil
        guard let testDefaults = UserDefaults(suiteName: testSuiteName) else {
            XCTFail("Failed to create UserDefaults suite for teardown")
            return
        }
        testDefaults.removePersistentDomain(forName: testSuiteName)
    }

    // MARK: - Field Management Tests

    func testCreateField() {
        // Arrange
        let fieldName = "Test Field Alpha"

        // Act
        let newField = dataManager.createField(name: fieldName)

        // Assert
        XCTAssertEqual(dataManager.fields.count, 1, "Should have 1 field after creation")
        XCTAssertEqual(dataManager.fields.first?.id, newField.id, "Created field ID should match")
        XCTAssertEqual(dataManager.fields.first?.name, fieldName, "Created field name should match")
        XCTAssertEqual(dataManager.selectedFieldID, newField.id, "Newly created field should be selected")
        XCTAssertTrue(dataManager.fields.first?.geoPoints.isEmpty ?? false, "New field should have no points")
    }

    func testDeleteField() {
        // Arrange
        let field1 = dataManager.createField(name: "FieldToDelete")
        let field2 = dataManager.createField(name: "FieldToKeep")
        XCTAssertEqual(dataManager.fields.count, 2)
        dataManager.selectedFieldID = field1.id // Select the field to be deleted

        // Act
        let deleted = dataManager.deleteField(fieldId: field1.id)

        // Assert
        XCTAssertTrue(deleted, "Deletion should succeed for existing field")
        XCTAssertEqual(dataManager.fields.count, 1, "Should have 1 field remaining")
        XCTAssertEqual(dataManager.fields.first?.id, field2.id, "The correct field should remain")
        XCTAssertEqual(dataManager.selectedFieldID, field2.id, "Selection should update to the remaining field")
    }

    func testDeleteNonExistentField() {
        // Arrange
        _ = dataManager.createField(name: "Existing Field")
        let nonExistentID = UUID()

        // Act
        let deleted = dataManager.deleteField(fieldId: nonExistentID)

        // Assert
        XCTAssertFalse(deleted, "Deletion should fail for non-existent field")
        XCTAssertEqual(dataManager.fields.count, 1, "Field count should remain unchanged")
    }

    // MARK: - GeoPoint Management Tests

    func testAddGeoPointToSelectedField() {
        // Arrange
        let field = dataManager.createField(name: "Point Field")
        dataManager.selectedFieldID = field.id // Ensure field is selected
        let coordinate = CLLocationCoordinate2D(latitude: 10.0, longitude: 20.0)
        let timestamp = Date()

        // Act
        let addedPoint = dataManager.addGeoPoint(coordinate: coordinate, elevation: 50.0, timestamp: timestamp, type: .marked, note: "Marker A", to: field.id)

        // Assert
        XCTAssertNotNil(addedPoint, "Adding point should return the created point")
        XCTAssertEqual(dataManager.fields.first?.geoPoints.count, 1, "Field should have 1 point")
        let pointInField = dataManager.fields.first?.geoPoints.first
        XCTAssertEqual(pointInField?.id, addedPoint?.id)
        XCTAssertEqual(pointInField?.latitude, coordinate.latitude)
        XCTAssertEqual(pointInField?.longitude, coordinate.longitude)
        XCTAssertEqual(pointInField?.altitude, 50.0, "Altitude should match the added value")
        XCTAssertEqual(pointInField?.timestamp, timestamp)
        XCTAssertEqual(pointInField?.pointType, .marked)
        XCTAssertEqual(pointInField?.note, "Marker A")
    }

    func testAddGeoPointToNonExistentField() {
        // Arrange
        let nonExistentID = UUID()
        let coordinate = CLLocationCoordinate2D(latitude: 10.0, longitude: 20.0)

        // Act
        let addedPoint = dataManager.addGeoPoint(coordinate: coordinate, elevation: nil, timestamp: Date(), type: .continuous, note: nil, to: nonExistentID)

        // Assert
        XCTAssertNil(addedPoint, "Adding point to non-existent field should return nil")
    }

    // MARK: - Area Calculation Tests (Simplified)

    func testCalculateArea_LessThan3Points_ReturnsNil() async {
        // Arrange
        let field = dataManager.createField(name: "Area Test Field")
        let p1 = CLLocationCoordinate2D(latitude: 10, longitude: 10)
        let p2 = CLLocationCoordinate2D(latitude: 11, longitude: 10)

        // Act & Assert (0 points)
        var area = await dataManager.calculateFieldArea(fieldId: field.id)
        XCTAssertNil(area, "Area should be nil for 0 points")

        // Act & Assert (1 point)
        _ = dataManager.addGeoPoint(coordinate: p1, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        area = await dataManager.calculateFieldArea(fieldId: field.id)
        XCTAssertNil(area, "Area should be nil for 1 point")

        // Act & Assert (2 points)
        _ = dataManager.addGeoPoint(coordinate: p2, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        area = await dataManager.calculateFieldArea(fieldId: field.id)
        XCTAssertNil(area, "Area should be nil for 2 points")
    }

    func testCalculateArea_SimpleTriangle_ReturnsArea() async throws {
        // Arrange
        let field = dataManager.createField(name: "Triangle Field")
        // Define a simple right triangle
        let p1 = CLLocationCoordinate2D(latitude: 0, longitude: 0)
        let p2 = CLLocationCoordinate2D(latitude: 0.001, longitude: 0) // Approx 111m North
        let p3 = CLLocationCoordinate2D(latitude: 0, longitude: 0.001) // Approx 111m East

        _ = dataManager.addGeoPoint(coordinate: p1, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        _ = dataManager.addGeoPoint(coordinate: p2, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        _ = dataManager.addGeoPoint(coordinate: p3, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)

        // Act
        let area = await dataManager.calculateFieldArea(fieldId: field.id)
        let unwrappedArea = try XCTUnwrap(area, "Area should not be nil for 3 points")

        // Assert
        // Expected area is approx 0.5 * base * height = 0.5 * 111m * 111m ~= 6160 m^2
        let expectedArea: Double = 6160.0
        let tolerance: Double = 300.0 // Allow tolerance
        XCTAssertGreaterThan(unwrappedArea, 0, "Area should be positive")
        XCTAssertEqual(unwrappedArea, expectedArea, accuracy: tolerance, "Area calculation for triangle is incorrect")
    }

    // MARK: - currentFieldArea Update Trigger Tests

    func testCurrentFieldArea_UpdatesOnSelectionChange() async {
        // Arrange
        let field1 = dataManager.createField(name: "Select Field 1")
        let field2 = dataManager.createField(name: "Select Field 2")
        // Add points to field1 for area calculation
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 1, longitude: 1), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field1.id)
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 2, longitude: 1), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field1.id)
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 2, longitude: 2), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field1.id)

        let initialExpectation = XCTestExpectation(description: "Wait for area calc after field 2 creation (should be nil)")
        // Field 2 is selected by default after creation
         DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { initialExpectation.fulfill() }
         await fulfillment(of: [initialExpectation], timeout: 2.0)
         XCTAssertNil(dataManager.currentFieldArea, "Area should be nil initially (field 2 selected)")

        // Act: Select field 1
        dataManager.selectedFieldID = field1.id
        let select1Expectation = XCTestExpectation(description: "Wait for area calc after selecting field 1")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { select1Expectation.fulfill() }
        await fulfillment(of: [select1Expectation], timeout: 2.0)

        // Assert: Area should be calculated for field 1
        XCTAssertNotNil(dataManager.currentFieldArea, "currentFieldArea should be calculated for field 1")

        // Act: Select field 2 again (no points)
        dataManager.selectedFieldID = field2.id
        let select2Expectation = XCTestExpectation(description: "Wait for area calc after selecting field 2")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { select2Expectation.fulfill() }
        await fulfillment(of: [select2Expectation], timeout: 2.0)

        // Assert: Area should be nil again
        XCTAssertNil(dataManager.currentFieldArea, "currentFieldArea should be nil after selecting field 2 again")
    }

    func testCurrentFieldArea_UpdatesOnPointAdded() async {
        // Arrange
        let field = dataManager.createField(name: "Point Add Test Field")
        dataManager.selectedFieldID = field.id
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 1, longitude: 1), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 2, longitude: 1), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)

        let expectNil = XCTestExpectation(description: "Wait for area (nil) after 2 points")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { expectNil.fulfill() }
        await fulfillment(of: [expectNil], timeout: 2.0)
        XCTAssertNil(dataManager.currentFieldArea, "Area should be nil after 2 points")

        // Act: Add 3rd point
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 2, longitude: 2), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)

        let expectNotNil = XCTestExpectation(description: "Wait for area calc after 3rd point")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { expectNotNil.fulfill() }
        await fulfillment(of: [expectNotNil], timeout: 2.0)

        // Assert: Area should be calculated
        XCTAssertNotNil(dataManager.currentFieldArea, "Area should be calculated after 3rd point")
    }

    func testCurrentFieldArea_SkipsUpdateDuringRecording() async {
        // Arrange
        let field = dataManager.createField(name: "Recording Skip Test")
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 1, longitude: 1), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 2, longitude: 1), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 2, longitude: 2), elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        dataManager.selectedFieldID = field.id

        let expectInitial = XCTestExpectation(description: "Wait for initial area")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { expectInitial.fulfill() }
        await fulfillment(of: [expectInitial], timeout: 2.0)
        let initialArea = dataManager.currentFieldArea
        XCTAssertNotNil(initialArea)

        // Act: Start recording and add another point
        dataManager.startContinuousRecording()
        _ = dataManager.addGeoPoint(coordinate: .init(latitude: 1, longitude: 2), elevation: nil, timestamp: Date(), type: .continuous, note: nil, to: field.id)

        let expectSkip = XCTestExpectation(description: "Wait after adding point during recording")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { expectSkip.fulfill() }
        await fulfillment(of: [expectSkip], timeout: 2.0)

        // Assert: Area should not have changed
        XCTAssertEqual(dataManager.currentFieldArea, initialArea, "Area should not recalculate during recording")

        // Act: Stop recording
        dataManager.stopContinuousRecording()
        // Adding a point *now* should trigger a recalc (tested implicitly by point add test)
    }

    // MARK: - Distance Calculation Tests

    func testCalculateTotalDistance() async {
        // Arrange
        let field = dataManager.createField(name: "Distance Test Field")
        let p1 = CLLocationCoordinate2D(latitude: 0.0, longitude: 0.0)
        let p2 = CLLocationCoordinate2D(latitude: 0.001, longitude: 0.0) // Approx 111m N
        let p3 = CLLocationCoordinate2D(latitude: 0.001, longitude: 0.001) // Approx 111m E

        // Act & Assert - 0 points
        var distance = dataManager.calculateTotalDistance(fieldId: field.id)
        XCTAssertEqual(distance, 0.0, "Distance should be 0 for 0 points")

        // Act & Assert - 1 point
        _ = dataManager.addGeoPoint(coordinate: p1, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        distance = dataManager.calculateTotalDistance(fieldId: field.id)
        XCTAssertEqual(distance, 0.0, "Distance should be 0 for 1 point")

        // Act & Assert - 2 points
        _ = dataManager.addGeoPoint(coordinate: p2, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        distance = dataManager.calculateTotalDistance(fieldId: field.id)
        let expectedDist1 = CLLocation(latitude: p1.latitude, longitude: p1.longitude).distance(from: .init(latitude: p2.latitude, longitude: p2.longitude))
        XCTAssertEqual(distance, expectedDist1, accuracy: 1.0, "Distance for 2 points is incorrect")

        // Act & Assert - 3 points
        _ = dataManager.addGeoPoint(coordinate: p3, elevation: nil, timestamp: Date(), type: .boundary, note: nil, to: field.id)
        distance = dataManager.calculateTotalDistance(fieldId: field.id)
        let expectedDist2 = expectedDist1 + CLLocation(latitude: p2.latitude, longitude: p2.longitude).distance(from: .init(latitude: p3.latitude, longitude: p3.longitude))
        XCTAssertEqual(distance, expectedDist2, accuracy: 1.0, "Distance for 3 points is incorrect")
    }

    // MARK: - Performance Tests

    func testCalculateAreaPerformance() async throws {
        // Arrange: Create a field with a decent number of points
        let field = dataManager.createField(name: "Performance Test Field")
        let numberOfPoints = 100 // Adjust as needed for meaningful measurement
        let centerLat = 40.0
        let centerLon = -75.0
        let radius = 0.001 // Small radius for points

        for i in 0..<numberOfPoints {
            let angle = Double(i) * 2.0 * Double.pi / Double(numberOfPoints)
            let lat = centerLat + radius * cos(angle)
            let lon = centerLon + radius * sin(angle)
            _ = dataManager.addGeoPoint(coordinate: .init(latitude: lat, longitude: lon),
                                        elevation: nil,
                                        timestamp: Date(),
                                        type: .boundary,
                                        note: nil,
                                        to: field.id)
        }
        // Ensure points are added before measuring
        XCTAssertEqual(dataManager.fields.first?.geoPoints.filter { $0.pointType == .boundary }.count, numberOfPoints)


        self.measure {
            // Act: Measure the area calculation
            // Need to run async code within measure block.
            // Use Task and expectation for older XCTest or rely on modern XCTest async support.
            // Assuming modern XCTest support for await within measure:
             let expectation = XCTestExpectation(description: "Calculate area within measure block")
             Task {
                 _ = await dataManager.calculateFieldArea(fieldId: field.id)
                 expectation.fulfill()
             }
             // Wait for the async task inside measure to complete.
             // This explicit wait might not be needed in the latest XCTest,
             // but it ensures the async operation finishes before the measurement block ends.
             wait(for: [expectation], timeout: 10.0) // Adjust timeout if needed
        }
    }
} 