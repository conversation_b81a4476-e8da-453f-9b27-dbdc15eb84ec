/*
//
//  CropCompass_GPSTests.swift
//  CropCompass_GPSTests
//
//  Created by <PERSON><PERSON> on 28/3/2025.
//

import XCTest
import CoreLocation
@testable import CropCompass_GPS

final class CropCompass_GPSTests: XCTestCase {
    
    // MARK: - Data Model Tests
    
    func testGeoPointInitialisation() {
        let coordinate = CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278)
        let elevation: CLLocationDistance? = 100.0
        let timestamp = Date()
        
        let point = GeoPoint(
            coordinate: coordinate,
            elevation: elevation,
            timestamp: timestamp,
            type: .marked,
            note: "Test Point"
        )
        
        XCTAssertEqual(point.latitude, 51.5074, accuracy: 0.000001)
        XCTAssertEqual(point.longitude, -0.1278, accuracy: 0.000001)
        XCTAssertEqual(point.elevation, elevation)
        XCTAssertEqual(point.timestamp, timestamp)
        XCTAssertEqual(point.pointType, .marked)
        XCTAssertEqual(point.note, "Test Point")
    }
    
    func testFieldManagement() {
        var field = Field(name: "Test Field")
        XCTAssertEqual(field.name, "Test Field")
        XCTAssertTrue(field.geoPoints.isEmpty)
        
        // Add a test point
        let point = GeoPoint(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            elevation: 100.0 as CLLocationDistance?,
            timestamp: Date(),
            type: .boundary,
            note: "North Corner"
        )
        
        field.geoPoints.append(point)
        XCTAssertEqual(field.geoPoints.count, 1)
        XCTAssertEqual(field.geoPoints[0].pointType, .boundary)
        XCTAssertEqual(field.geoPoints[0].note, "North Corner")
    }
    
    func testFarmManagement() {
        var farm = Farm(name: "Test Farm")
        XCTAssertEqual(farm.name, "Test Farm")
        XCTAssertTrue(farm.fields.isEmpty)
        
        let field = Field(name: "Test Field")
        farm.fields.append(field)
        
        XCTAssertEqual(farm.fields.count, 1)
        XCTAssertEqual(farm.fields[0].name, "Test Field")
    }
    
    // MARK: - Data Manager Tests
    
    var dataManager: DataManager!
    
    override func setUp() {
        super.setUp()
        dataManager = DataManager()
        // Clear any existing data
        dataManager.farms.removeAll()
    }
    
    override func tearDown() {
        dataManager.farms.removeAll()
        dataManager = nil
        super.tearDown()
    }
    
    func testFarmCreation() {
        let farmName = "New Test Farm"
        let farm = dataManager.createFarm(name: farmName)
        
        XCTAssertNotNil(farm)
        XCTAssertEqual(dataManager.farms.count, 1)
        XCTAssertEqual(dataManager.farms[0].name, farmName)
    }
    
    func testFieldCreation() {
        let farmName = "Test Farm"
        let fieldName = "Test Field"
        
        let farm = dataManager.createFarm(name: farmName)
        XCTAssertNotNil(farm, "Farm creation failed")
        
        let field = dataManager.createField(name: fieldName, for: farm.id)
        XCTAssertNotNil(field, "Field creation failed")
        
        guard let createdField = dataManager.farms[0].fields.first else {
            XCTFail("Field not found in farm")
            return
        }
        
        XCTAssertEqual(createdField.name, fieldName)
    }
    
    func testGeoPointAddition() {
        // Setup farm and field
        let farm = dataManager.createFarm(name: "Test Farm")
        XCTAssertNotNil(farm, "Farm creation failed")
        
        let field = dataManager.createField(name: "Test Field", for: farm.id)
        XCTAssertNotNil(field, "Field creation failed")
        guard let createdField = field else {
            XCTFail("Field creation returned nil unexpectedly.")
            return
        }
        
        // Add a boundary point
        let coordinate = CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278)
        let point = dataManager.addGeoPoint(
            coordinate: coordinate,
            elevation: 100.0 as CLLocationDistance?,
            timestamp: Date(),
            type: .boundary,
            note: "North Corner",
            to: createdField.id,
            in: farm.id
        )
        
        XCTAssertNotNil(point, "GeoPoint creation failed")
        
        guard let createdPoint = dataManager.farms[0].fields[0].geoPoints.first else {
            XCTFail("GeoPoint not found in field")
            return
        }
        
        XCTAssertEqual(createdPoint.pointType, .boundary)
        XCTAssertEqual(createdPoint.note, "North Corner")
    }
    
    // MARK: - GPX Export Tests
    
    func testGPXExport() {
        // Create test data
        let farm = Farm(name: "Export Test Farm")
        var field = Field(name: "Export Test Field")
        
        let point = GeoPoint(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            elevation: 100.0 as CLLocationDistance?,
            timestamp: Date(),
            type: .boundary,
            note: "North Corner"
        )
        
        field.geoPoints.append(point)
        var farmWithField = farm
        farmWithField.fields.append(field)
        
        // Test single field export
        let fieldGPX = GPXExporter.exportField(field, in: farm)
        XCTAssertTrue(fieldGPX.contains("<trkpt lat=\"51.5074\" lon=\"-0.1278\">"))
        XCTAssertTrue(fieldGPX.contains("<ele>100.0</ele>"))
        XCTAssertTrue(fieldGPX.contains("<cmt>North Corner</cmt>"))
        XCTAssertTrue(fieldGPX.contains("<name>Export Test Field</name>"))
        
        // Test farm export
        let farmGPX = GPXExporter.exportFarm(farmWithField)
        XCTAssertTrue(farmGPX.contains("<name>Farm: Export Test Farm</name>"))
        XCTAssertTrue(farmGPX.contains("<name>Export Test Field</name>"))
    }
    
    // MARK: - GPS Signal Strength Tests
    
    func testSignalStrengthCalculation() {
        let locationManager = LocationManager()
        
        // Test no signal (negative accuracy)
        let noSignalLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            altitude: 100.0,
            horizontalAccuracy: -1.0,
            verticalAccuracy: -1.0,
            timestamp: Date()
        )
        locationManager.location = noSignalLocation
        locationManager.updateSignalStrength()
        XCTAssertEqual(locationManager.signalStrength, .none)
        
        // Test excellent signal (<10m accuracy)
        let excellentLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            altitude: 100.0,
            horizontalAccuracy: 5.0,
            verticalAccuracy: 8.0,
            timestamp: Date()
        )
        locationManager.location = excellentLocation
        locationManager.updateSignalStrength()
        XCTAssertEqual(locationManager.signalStrength, .excellent)
        
        // Test good signal (10-20m accuracy)
        let goodLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            altitude: 100.0,
            horizontalAccuracy: 15.0,
            verticalAccuracy: 18.0,
            timestamp: Date()
        )
        locationManager.location = goodLocation
        locationManager.updateSignalStrength()
        XCTAssertEqual(locationManager.signalStrength, .good)
        
        // Test moderate signal (20-50m accuracy)
        let moderateLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            altitude: 100.0,
            horizontalAccuracy: 35.0,
            verticalAccuracy: 40.0,
            timestamp: Date()
        )
        locationManager.location = moderateLocation
        locationManager.updateSignalStrength()
        XCTAssertEqual(locationManager.signalStrength, .moderate)
        
        // Test poor signal (>50m accuracy)
        let poorLocation = CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278),
            altitude: 100.0,
            horizontalAccuracy: 60.0,
            verticalAccuracy: 70.0,
            timestamp: Date()
        )
        locationManager.location = poorLocation
        locationManager.updateSignalStrength()
        XCTAssertEqual(locationManager.signalStrength, .poor)
    }
}
*/
