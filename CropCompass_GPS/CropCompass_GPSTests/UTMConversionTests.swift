import XCTest
import CoreLocation
@testable import CropCompass_GPS // Ensure this matches your module name

final class UTMConversionTests: XCTestCase {

    // Test with a known coordinate (e.g., Harare, Zimbabwe)
    // Source: Online UTM converter (verify with a reliable one)
    // Lat: -17.8252, Lon: 31.0335
    // Expected UTM: Zone 36S, Easting: 286598, Northing: 8028565 (approx)
    func testUTMConversion_Harare_CorrectResult() {
        // Arrange
        let harareCoord = CLLocationCoordinate2D(latitude: -17.8252, longitude: 31.0335)
        let expectedZone: UInt = 36
        let expectedHemisphere: UTMHemisphere = .southern
        let expectedEasting: Double = 286598
        let expectedNorthing: Double = 8028565
        let tolerance: Double = 1.0 // Allow 1 meter tolerance

        // Act
        let utmResult = harareCoord.toUTMCoordinate()

        // Assert
        XCTAssertEqual(utmResult.zone, expectedZone, "UTM Zone is incorrect")
        XCTAssertEqual(utmResult.hemisphere, expectedHemisphere, "UTM Hemisphere is incorrect")
        XCTAssertEqual(utmResult.easting, expectedEasting, accuracy: tolerance, "UTM Easting is incorrect")
        XCTAssertEqual(utmResult.northing, expectedNorthing, accuracy: tolerance, "UTM Northing is incorrect")
    }

    // Test with a coordinate in the Northern Hemisphere (e.g., London, UK)
    // Lat: 51.5074, Lon: -0.1278
    // Expected UTM: Zone 30U (which is 30 Northern), Easting: 698114, Northing: 5710169 (approx)
    // Note: Easting/Northing can vary slightly depending on the specific UTM calculator/ellipsoid details.
    func testUTMConversion_London_CorrectResult() {
        // Arrange
        let londonCoord = CLLocationCoordinate2D(latitude: 51.5074, longitude: -0.1278)
        let expectedZone: UInt = 30
        let expectedHemisphere: UTMHemisphere = .northern
        // Using common values, might need slight adjustment based on the exact algorithm vs reference.
        // Sample from https://www.latlong.net/lat-long-utm.html for comparison (adjusting for precision)
        let expectedEasting: Double = 698114
        let expectedNorthing: Double = 5710169
        let tolerance: Double = 5.0 // Allow 5 meter tolerance

        // Act
        let utmResult = londonCoord.toUTMCoordinate()

        // Assert
        XCTAssertEqual(utmResult.zone, expectedZone, "UTM Zone is incorrect")
        XCTAssertEqual(utmResult.hemisphere, expectedHemisphere, "UTM Hemisphere is incorrect")
        XCTAssertEqual(utmResult.easting, expectedEasting, accuracy: tolerance, "UTM Easting is incorrect")
        XCTAssertEqual(utmResult.northing, expectedNorthing, accuracy: tolerance, "UTM Northing is incorrect")
    }

    // Test near the equator
    func testUTMConversion_Equator_CorrectHemisphereAndNorthing() {
         // Arrange
         let justAboveEquator = CLLocationCoordinate2D(latitude: 0.0001, longitude: 30.0)
         let justBelowEquator = CLLocationCoordinate2D(latitude: -0.0001, longitude: 30.0)
         let onEquator = CLLocationCoordinate2D(latitude: 0.0, longitude: 30.0)

         // Act
         let resultAbove = justAboveEquator.toUTMCoordinate()
         let resultBelow = justBelowEquator.toUTMCoordinate()
         let resultOn = onEquator.toUTMCoordinate()


         // Assert
         XCTAssertEqual(resultAbove.hemisphere, .northern, "Point just above equator should be Northern Hemisphere")
         XCTAssertEqual(resultBelow.hemisphere, .southern, "Point just below equator should be Southern Hemisphere")
         XCTAssertEqual(resultOn.hemisphere, .northern, "Point exactly on equator should be Northern Hemisphere by convention")

         // Northing should be small positive for Northern, large positive (10M offset) for Southern
         XCTAssertGreaterThan(resultAbove.northing, 0)
         XCTAssertLessThan(resultAbove.northing, 1000, "Northing just above equator should be small")
         XCTAssertGreaterThan(resultBelow.northing, 9_999_000, "Northing just below equator should be large (near 10M)")
         XCTAssertLessThan(resultBelow.northing, 10_000_000)
         XCTAssertGreaterThanOrEqual(resultOn.northing, 0)
         XCTAssertLessThan(resultOn.northing, 100, "Northing on equator should be near 0")
     }

    // Add more tests for edge cases if needed (e.g., near poles, zone boundaries)
} 