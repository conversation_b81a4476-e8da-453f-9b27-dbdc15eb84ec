# CropCompass GPS Manual Testing Checklist

## GPS Accuracy Testing

### Basic GPS Functionality
- [ ] Verify GPS signal acquisition time
- [ ] Check accuracy indicator colour changes:
  - [ ] Red (>10m accuracy)
  - [ ] Orange (5-10m accuracy)
  - [ ] Green (<5m accuracy)
- [ ] Verify elevation data accuracy against known reference points
- [ ] Test GPS updates frequency at different movement speeds

### Continuous Recording Mode
- [ ] Test different distance filter settings:
  - [ ] Walking (5m filter)
  - [ ] Cycling (10m filter)
  - [ ] Tractor (15m filter)
  - [ ] Vehicle (25m filter)
- [ ] Verify point spacing matches selected filter
- [ ] Check track smoothness at different speeds
- [ ] Validate elevation profile consistency

### Boundary Point Feature
- [ ] Test marking boundary points:
  - [ ] North corner
  - [ ] East corner
  - [ ] South corner
  - [ ] West corner
- [ ] Verify accuracy when marking points whilst stationary
- [ ] Check boundary point visibility on map
- [ ] Validate boundary point note persistence

## Environment Testing

### Different Environments
- [ ] Open field (optimal conditions)
- [ ] Near tree lines
- [ ] Near buildings/structures
- [ ] Valley/depression areas
- [ ] Elevated positions

### Weather Conditions
- [ ] Clear sky
- [ ] Overcast
- [ ] Light rain
- [ ] Heavy rain
- [ ] Early morning/late evening

## Device Compatibility Testing

### Device Models
- [ ] iPhone 14 Pro/Pro Max
- [ ] iPhone 14/13/12
- [ ] iPad (if applicable)
- [ ] Test on older devices (iPhone 11 or earlier)

### iOS Versions
- [ ] Latest iOS version
- [ ] Previous major iOS version
- [ ] Minimum supported iOS version

## Feature Integration Testing

### Farm/Field Management
- [ ] Create new farm
- [ ] Create multiple fields
- [ ] Switch between fields during recording
- [ ] Delete farm/field with recorded data

### Map Visualization
- [ ] Verify different point types display correctly:
  - [ ] Continuous track points (blue)
  - [ ] Manual marked points (red)
  - [ ] Boundary points (orange)
- [ ] Check map zooming and panning
- [ ] Verify track line rendering
- [ ] Test point selection/information display

### Data Export
- [ ] Export single field
- [ ] Export multiple fields
- [ ] Export entire farm
- [ ] Validate GPX file format
- [ ] Check boundary point metadata in export

## Performance Testing

### Battery Usage
- [ ] Monitor battery drain during continuous recording
- [ ] Check battery usage with different GPS accuracy settings
- [ ] Test battery impact of map display

### Memory Usage
- [ ] Record large datasets (>1000 points)
- [ ] Test with multiple fields/farms
- [ ] Monitor app memory usage during extended recording

### Storage
- [ ] Check storage usage for large datasets
- [ ] Verify data persistence across app restarts
- [ ] Test data backup/restore functionality

## Error Handling

### GPS Signal Loss
- [ ] Test behaviour when GPS signal is lost
- [ ] Verify recording resumes correctly when signal returns
- [ ] Check error messages and UI feedback

### Invalid Operations
- [ ] Attempt to record without selected field
- [ ] Try to export empty fields
- [ ] Test boundary point creation without GPS fix

### Edge Cases
- [ ] Test date line crossing (if applicable)
- [ ] Verify handling of extreme coordinates
- [ ] Check elevation data at sea level/high altitude

## Documentation Testing

### In-App Help
- [ ] Verify accuracy indicator explanation
- [ ] Check boundary point marking instructions
- [ ] Validate export format documentation

### Error Messages
- [ ] Verify all error messages are clear and helpful
- [ ] Check error recovery instructions
- [ ] Test error message localisation

## Notes
- Record any unexpected behaviour
- Note device and iOS version for any issues
- Document GPS accuracy in different conditions
- Track battery consumption patterns
- Note any usability concerns 