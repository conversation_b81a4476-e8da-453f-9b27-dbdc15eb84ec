# CropCompass_GPS Project Plan

## 1. Overview

An iOS application built with Swift and SwiftUI to accurately track GPS points (latitude, longitude, elevation) for farm fields. The app allows users to record data continuously (with adjustable intervals suitable for walking, cycling, tractor, or car) or manually. Users can associate notes with specific fields. Recorded data can be exported in GPX format.

### App Versions

The application will be developed in two distinct versions:

1. **Standalone Version (GPS Essentials):** [IMPLEMENTED]
   * Focus on accurate GPS data collection and field mapping
   * GPX export functionality
   * Basic farm/field organization
   * No web integration required
   * Optimized for offline use in remote areas

2. **Full Version (Web Integration):** [PLANNED]
   * All features from the standalone version
   * Synchronization with the CropCompass web application
   * Upload field data to web platform for advanced analysis
   * Receive and display recommendations from the web application
   * User authentication and subscription management integration

## 2. Core Functionality

*   **Accurate GPS Tracking:** Leverage CoreLocation for high-accuracy positioning, incorporating data from various sensors.
*   **Data Organisation:** Structure data by Farm -> Field -> GeoPoint.
*   **Recording Modes:**
    *   **Continuous:** Automatically records points based on distance/time intervals configured for different activity types.
    *   **Manual:** Records a single point upon user request.
*   **Field Notes:** Allow users to add textual notes per field.
*   **GPX Export:** Generate standard GPX files containing the recorded track data for each field.
*   **Web Integration (Full Version):** Connect with the CropCompass web application to leverage its advanced analytics and recommendation features.

## 3. Development Plan & Checklist

Here's a breakdown of the development steps:

-   [X] **1. Project Setup:**
    -   [X] Create a new iOS project using Xcode (SwiftUI template).
    -   [X] Configure basic project settings.
    -   [X] Add necessary location permission strings to `Info.plist`.
    -   [X] Add Background Modes capability for Location updates.
-   [X] **2. Core Location Integration:**
    -   [X] Create `LocationManager` class.
    -   [X] Implement location permission request logic.
    -   [X] Configure `CLLocationManager` for high accuracy.
    -   [X] Start/stop location updates.
    -   [X] Process incoming location data (latitude, longitude, elevation, timestamp).
    -   [X] Add configurable distance filter settings and activity type.
    -   [ ] Re-enable and debug background location updates (`allowsBackgroundLocationUpdates`).
-   [X] **3. Data Modelling:**
    -   [X] Define `GeoPoint` struct (lat, lon, ele, timestamp, id).
    -   [X] Define `Field` struct (id/name, notes, array of `GeoPoint`).
    -   [X] Define `Farm` struct (name, dictionary/array of `Field`).
    -   [X] Ensure data structures are `Codable` for persistence.
-   [X] **4. Recording Logic:**
    -   [X] Implement state management for recording (idle, recording-continuous, recording-manual).
    -   [X] Create `DataManager` to hold `Farm` data and manage selected Field.
    *   [X] **Continuous Mode:**
        -   [X] Implement logic to add `GeoPoint` based on `distanceFilter` and/or time interval.
        -   [X] Add settings UI to adjust `distanceFilter` and `activityType` (presets for walk, cycle, tractor, car).
    *   [X] **Manual Mode:**
        -   [X] Implement button action to capture current location and add `GeoPoint`.
-   [X] **5. UI Development (SwiftUI):**
    -   [X] **Main View:**
        -   [X] Display current location data (lat, lon, ele, accuracy).
        -   [X] Show recording status.
        -   [X] Display Start/Stop recording buttons.
        -   [X] Display Request Permission button (in Settings).
        -   [X] Add Manual Record Point button.
        -   [X] Selector for current Farm/Field.
    -   [X] **Settings/Management View:**
        -   [X] Create/Select Farm.
        -   [X] Create/Select Field within the current Farm.
        -   [X] View/Edit notes for the selected Field.
        -   [X] Configure recording settings (mode, interval presets).
    -   [X] **Data List/Export View:**
        -   [X] List recorded Farms/Fields/Points.
        -   [X] Trigger GPX export.
    -   [X] **Map View:**
        -   [X] Integrate `MapKit` to visualise recorded points for the selected field.
-   [X] **6. Data Persistence:**
    -   [X] Choose persistence method (UserDefaults for simplicity in v1).
    -   [X] Implement saving `Farm` data.
    -   [X] Implement loading `Farm` data on app launch.
-   [X] **7. GPX Export:**
    -   [X] Create `GPXExporter` class/functions.
    -   [X] Implement logic to format `Farm`/`Field`/`GeoPoint` data into valid GPX XML structure.
        -   [X] Map `Field` to `<trk>`.
        -   [X] Map `GeoPoint` array to `<trkseg>` containing `<trkpt lat="..." lon="...">`.
        -   [X] Include `<ele>`, `<time>`, and `<cmt>` (for notes) tags.
    -   [X] Add file export functionality using iOS share sheet.

## 4. Web Integration Features (Full Version)

-   [ ] **1. User Authentication:**
    -   [ ] Implement login/registration screens.
    -   [ ] Integrate with web application authentication using JWT/OAuth.
    -   [ ] Store authentication tokens securely in keychain.
    -   [ ] Handle expired tokens and refresh logic.

-   [ ] **2. Data Synchronization:**
    -   [ ] Create network manager for API communication.
    -   [ ] Implement field data upload to web application.
    -   [ ] Handle offline mode with local queuing of changes.
    -   [ ] Sync conflict resolution strategy.
    -   [ ] Background upload/download tasks.

-   [ ] **3. Recommendations Display:**
    -   [ ] UI components to show recommendations from web platform.
    -   [ ] Display water requirements, plant spacing, and other calculations.
    -   [ ] Visualize field-specific insights and actionable data.
    -   [ ] Notifications for updated recommendations.

-   [ ] **4. Subscription Management:**
    -   [ ] Display current subscription tier.
    -   [ ] Show available features based on subscription level.
    -   [ ] In-app purchase options for upgrading (if applicable).
    -   [ ] Handle subscription status changes and limitations.

## 5. Implementation Checklist

### Phase 1: Standalone Version (GPS Essentials)
- [X] **Sprint 1: Core GPS Functionality**
  - [X] Implement recording state management (idle, continuous, manual)
  - [X] Add manual point recording functionality
  - [X] Add continuous recording with configurable filters
  - [ ] Complete background location updates implementation (planned for v1.1)

- [X] **Sprint 2: Data Management**
  - [X] Complete Farm/Field management UI
  - [X] Implement data persistence using Codable and UserDefaults
  - [X] Add data export functionality (GPX)
  - [X] Create basic visualizations of recorded tracks

- [X] **Sprint 3: UI Refinement**
  - [X] Add tab-based navigation for better organization
  - [X] Improve recording interface with visual feedback
  - [X] Add settings screen for GPS configuration
  - [X] Add map visualization of recorded fields
  - [X] Create comprehensive export options

- [ ] **Sprint 4: Testing & Final Polish (Current)**
  - [ ] Comprehensive testing across iOS devices
  - [ ] Optimize battery usage during recording
  - [ ] Add better error handling and recovery
  - [ ] Improve offline mode indicators
  - [ ] Prepare for App Store submission

### Phase 2: Full Version (Web Integration)
- [ ] **Sprint 4: Authentication & Basic Connectivity**
  - [ ] Implement authentication flow with web platform
  - [ ] Create network layer for API communication
  - [ ] Add basic field data synchronization
  - [ ] Implement offline queuing system

- [ ] **Sprint 5: Data Synchronization**
  - [ ] Complete bidirectional sync for all entities
  - [ ] Add conflict resolution
  - [ ] Implement background sync
  - [ ] Add progress indicators for sync operations

- [ ] **Sprint 6: Recommendations & Premium Features**
  - [ ] Create UI for displaying web-calculated recommendations
  - [ ] Add visualization components for insights
  - [ ] Implement subscription-aware feature toggling
  - [ ] Add in-app notifications for new recommendations

- [ ] **Sprint 7: Polish & Launch**
  - [ ] Final UI refinements
  - [ ] Performance optimizations
  - [ ] Full integration testing
  - [ ] App Store submission for full version

## 6. Data Structure for Export (GPX)

While the initial request mentioned a specific columnar format, we will prioritise standard GPX format for compatibility. Each field's track will be exported as a `<trk>` element within the GPX file.

*   **`<trk>`:** Represents a Field.
    *   **`<n>`:** Field ID/Name.
    *   **`<trkseg>`:** Contains the sequence of points for the field.
        *   **`<trkpt lat=".." lon="..">`:** Represents a GeoPoint.
            *   **`<ele>`:** Elevation.
            *   **`<time>`:** Timestamp (ISO 8601 format).
            *   **(Optional Custom Extension):** Could add `<extensions><gpxtpx:TrackPointExtension><gpxtpx:hr>...</gpxtpx:hr></gpxtpx:TrackPointExtension></extensions>` or similar for PointID if strictly needed, but standard parsers might ignore it. Farm name might be included in the overall GPX metadata or the filename.

## 7. Target Platform

*   iOS (latest version recommended)

## 8. Key Frameworks

*   SwiftUI (UI)
*   CoreLocation (GPS & Positioning)
*   MapKit (Map Visualisation) - Implemented
*   Foundation (Data handling, File I/O, Codable)
*   URLSession (Network requests for Full Version) - Planned
*   KeychainAccess (Secure storage of credentials for Full Version) - Planned

## 9. Current Implementation Status & Handover Notes (April 2025)

### Implementation Overview

The CropCompass GPS application is currently in Sprint 4 of Phase 1 (Standalone Version). All core functionality for the standalone version has been implemented, with the exception of background location updates which are planned for v1.1.

### Recently Completed Features

- **Boundary Point Feature**: Added functionality to mark corner points of fields without starting continuous recording
  - Added 'boundary' point type to the PointType enum
  - Enhanced MarkSpotNoteView with segmented picker for point type selection
  - Added corner-specific tags (North, East, South, West) for better identification
  - Updated MapView to display boundary points as distinctive orange square markers

- **Bug Fixes**:
  - Fixed ForEach initializer in MapView by explicitly specifying the id parameter
  - Corrected conditional binding for altitude in MarkSpotNoteView

### Completed Core Features

The standalone version (GPS Essentials) is now functionally complete with the following features implemented:

1. **Tab-Based Navigation System**
   * Record tab: For GPS data collection and field mapping
   * Map tab: For visualization of recorded points and tracks
   * Export tab: For exporting data in GPX format
   * Settings tab: For configuration of app behavior

2. **Farm and Field Management**
   * Create and select farms
   * Create and select fields within farms
   * Organize GPS data hierarchically

3. **GPS Recording**
   * Continuous recording mode with configurable distance filters
   * Activity type optimization (walking, cycling, vehicle, tractor)
   * Manual point marking with optional notes
   * Real-time display of GPS accuracy and coordinates

4. **Data Visualization**
   * Interactive map view of recorded fields
   * Distinction between continuous tracks and manually marked points
   * Track statistics (distance, point count)

5. **Data Export**
   * Standard GPX format export
   * Options to export individual fields or entire farms
   * File sharing via iOS share sheet

### Known Issues

- Background location updates are not yet fully implemented and debugged
- The app has not been extensively tested on all iOS device sizes and models
- Battery optimization during long recording sessions needs improvement

### Next Steps

1. **Testing & Validation**:
   - Perform comprehensive testing across different iOS devices
   - Validate GPS accuracy in various environments (open fields, near buildings, etc.)
   - Test boundary point functionality in real-world scenarios

2. **Performance Optimization**:
   - Optimize battery usage during continuous recording
   - Improve data storage efficiency for large datasets

3. **UI/UX Enhancements**:
   - Add visual indicators for GPS signal strength
   - Improve map visualization with additional details (area calculation, distance measurement)
   - Enhance the export process with more format options

4. **Future Features for v1.1**:
   - Complete implementation of background location updates
   - Add area calculation for fields based on boundary points
   - Implement data backup and restore functionality
   - Prepare for App Store submission

### Development Environment

- **Xcode Version**: Latest stable release
- **iOS Target**: iOS 16.0+
- **Swift Version**: Swift 5.7+
- **Device Testing**: Primary testing on iPhone 14 Pro and newer simulators
