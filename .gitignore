.DS_Store
PROJECT_PLAN.md
system_errors.txt
CropCompass_GPS.code-workspace

# Xcode
#
# gitignore contributors: remember to update Global/Xcode.gitignore, Objective-C.gitignore & Swift.gitignore

## User settings
xcuserdata/
*.xcuserstate

## Xcode project files
CropCompass_GPS/CropCompass_GPS.xcodeproj/
*.xcodeproj/project.xcworkspace/
*.xcodeproj/xcuserdata/
*.xcworkspace/xcuserdata/

## Xcode 8 and earlier
*.xcscmblueprint
*.xccheckout

## Obj-C/Swift specific
*.hmap

## App packaging
*.ipa
*.dSYM.zip
*.dSYM

# Swift Package Manager
#
.build/

# CocoaPods
#
Pods/
*.xcworkspace

# Carthage
#
Carthage/Build/

# Build files
build/
DerivedData/
*.o
*.LinkFileList
*.swiftdeps
*.swiftmodule
*.swiftsourceinfo
*.d
*.dia
*.pcm
*.stringsdata
*.xcbuilddata

# SwiftUI Previews
*.preview
*.preview.swift
*.preview.swiftui
*.preview.swiftui.swift
*.preview.swiftui.swiftui

# Temporary files
*.new
*.bak
*.tmp
*.old
